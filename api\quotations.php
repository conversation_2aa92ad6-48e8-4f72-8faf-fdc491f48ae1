<?php
include '../config.php';

// get data by $_GET['id']
if (isset($_GET['id'])) {
    $id = $_GET['id'];
    $sql = "SELECT *
            FROM quotations
            WHERE id = $quotation_id";
    $result = mysqli_query($conn, $sql);
    $data = [];

    while ($row = mysqli_fetch_assoc($result)) {
        $data[] = $row;
    }
    echo json_encode($data);
} else if (isset($_GET['customer_id'])) {
    $customer_id = $_GET['customer_id'];
    $sql = "SELECT * FROM quotations WHERE customer_id = $customer_id ";
    $result = mysqli_query($conn, $sql);
    $data = [];

    while ($row = mysqli_fetch_assoc($result)) {
        $data[] = $row;
    }
    echo json_encode($data);
} else {
    echo json_encode(['error' => 'Invalid request']);
}