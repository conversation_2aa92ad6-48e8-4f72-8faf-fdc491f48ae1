<?php
include '../config.php';
include '../auth.php';

// get data by $_GET['id']
if (isset($_GET['id'])) {
    $id = $_GET['id'];
    $sql = "SELECT *
            FROM bills
            WHERE id = $id";
    $result = mysqli_query($conn, $sql);
    $data = [];

    while ($row = mysqli_fetch_assoc($result)) {
        $data[] = $row;
    }
    echo json_encode($data);
} else if (isset($_GET['customer_id'])) {
    $customer_id = $_GET['customer_id'];
    $sql = "SELECT * FROM bills WHERE customer_id = '$customer_id' 
    AND status_void = 'NO' 
    AND receipt_id = 0 
    ORDER BY document_number ASC";
    $result = mysqli_query($conn, $sql);
    $data = [];

    while ($row = mysqli_fetch_assoc($result)) {
        $data[] = $row;
    }
    if (!empty($data)) {
        echo json_encode($data);
    } else {
        echo json_encode(['message' => 'ไม่มีข้อมูล']);
    }
    
}
// get bills data by $_GET['id ']
elseif (isset($_GET['bill_id'])) {
    $bill_id = $_GET['bill_id'];
    $sql = "SELECT b.* FROM bills b 
    INNER JOIN bill_details d 
    ON b.id=d.bill_id 
    WHERE bill_id = $bill_id AND status_void = 'NO'";
    $result = mysqli_query($conn, $sql);
    $data = [];

    while ($row = mysqli_fetch_assoc($result)) {
        $data[] = $row;
    }
    echo json_encode($data);
} else {
    echo json_encode(array('status' => 'error', 'message' => 'Invalid request'));
}

