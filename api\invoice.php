<?php
include '../config.php';
include '../auth.php';

// get invoices data by $_GET['customer_id']
if (isset($_GET['customer_id'])) {
    $customer_id = $_GET['customer_id'];
    
    // Base WHERE clause
    $whereClause = "customer_id = '$customer_id' AND bill_id = 0 AND status_void = 'NO'";
    
    // Add search condition if search parameter is provided
    if (isset($_GET['search']) && !empty($_GET['search'])) {
        $search = mysqli_real_escape_string($conn, $_GET['search']);
        $whereClause .= " AND (
            document_number LIKE '%$search%' OR 
            volume_number LIKE '%$search%' OR 
            purchase_order LIKE '%$search%' OR
            document_date LIKE '%$search%'
        )";
    }
    
    // Check if only count is requested
    if (isset($_GET['count_only']) && $_GET['count_only'] == '1') {
        $countSql = "SELECT COUNT(*) as total FROM invoices WHERE $whereClause";
        $countResult = mysqli_query($conn, $countSql);
        $countRow = mysqli_fetch_assoc($countResult);
        echo $countRow['total'];
        exit;
    }
    
    // Build the main query
    $sql = "SELECT * FROM invoices WHERE $whereClause ORDER BY document_number ASC";
    
    // Add pagination if limit and offset are provided
    if (isset($_GET['limit']) && isset($_GET['offset'])) {
        $limit = (int)$_GET['limit'];
        $offset = (int)$_GET['offset'];
        $sql .= " LIMIT $limit OFFSET $offset";
    }
    
    $result = mysqli_query($conn, $sql);
    $data = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $data[] = $row;
    }
    
    if (!empty($data)) {
        echo json_encode($data);
    } else {
        echo json_encode([]);
    }
}

