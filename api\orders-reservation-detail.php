<?php
include '../config.php';
include '../auth.php';

// get data by $_GET['id']
if (isset($_GET['orders_reservation_id'])) {
    $orders_reservation_id = $_GET['orders_reservation_id'];
    $sql = "SELECT d.*, p.profile_image
            FROM orders_reservation_details d
            INNER JOIN products_orders p ON d.product_id=p.id
            WHERE orders_reservation_id = $orders_reservation_id";
    $result = mysqli_query($conn, $sql);
    $data = [];

    while ($row = mysqli_fetch_assoc($result)) {
        $data[] = $row;
    }
    echo json_encode($data);
}