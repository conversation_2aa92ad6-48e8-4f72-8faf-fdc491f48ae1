<?php
header('Content-Type: application/json');
include '../config.php';
include '../auth.php';

try {
    // ตรวจสอบว่ามี parameter id หรือไม่ (สำหรับดึงข้อมูลแมททีเรียลรายเดียว)
    if (isset($_GET['id']) && !empty($_GET['id'])) {
        $material_id = intval($_GET['id']);
        $query = "SELECT id, material_code, material_name, description, status 
                  FROM materials 
                  WHERE id = $material_id";
        $result = mysqli_query($conn, $query);
        
        if ($result && mysqli_num_rows($result) > 0) {
            $material = mysqli_fetch_assoc($result);
            echo json_encode($material);
        } else {
            echo json_encode(['error' => 'ไม่พบข้อมูลแมททีเรียล']);
        }
    } else {
        // ดึงข้อมูลแมททีเรียลทั้งหมด
        $query = "SELECT id, material_code, material_name, description, status 
                  FROM materials 
                  WHERE status = 'active' 
                  ORDER BY material_code ASC, material_name ASC";
        $result = mysqli_query($conn, $query);
        
        $materials = [];
        if ($result) {
            while ($row = mysqli_fetch_assoc($result)) {
                $materials[] = $row;
            }
        }
        
        echo json_encode($materials);
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => 'เกิดข้อผิดพลาดในการดึงข้อมูล: ' . $e->getMessage()]);
}
?>