<?php
include '../config.php';
include '../auth.php';

// get all process data
if (!isset($_GET['customer_id']) && !isset($_GET['reservation_id'])) {
    $sql = "SELECT * FROM process ORDER BY process_name";
    $result = mysqli_query($conn, $sql);
    $data = array();
    while ($row = mysqli_fetch_assoc($result)) {
        $data[] = $row;
    }
    echo json_encode($data);
    exit;
}

// get product data by $_GET['customer_id']
if (isset($_GET['customer_id'])) {
    $customer_id = $_GET['customer_id'];
    $sql = "SELECT * FROM products WHERE customer_id = '$customer_id'";
    $result = mysqli_query($conn, $sql);
    $data = array();
    while ($row = mysqli_fetch_assoc($result)) {
        $data[] = $row;
    }
    echo json_encode($data);
}

// get product data by $_GET['reservation_id']
elseif (isset($_GET['reservation_id'])) {
    $reservation_id = $_GET['reservation_id'];
    // ดึงสินค้าทั้งหมดที่เกี่ยวข้องกับ reservation_id จาก reservation_details
    $sql = "SELECT p.* FROM products p 
            INNER JOIN reservation_details rd ON p.product_code = rd.product_code
            WHERE rd.reservation_id = '$reservation_id' AND p.quantity > 0 ";
    $result = mysqli_query($conn, $sql);
    $data = array();
    while ($row = mysqli_fetch_assoc($result)) {
        $data[] = $row;
    }
    echo json_encode($data);
}

















