<?php
include '../config.php';
include '../auth.php';

// Set content type to JSON
header('Content-Type: application/json');

// Function to generate next number
function generateNextNumber($conn, $prefix) {
    $query = "SELECT current_number, format FROM running_numbers WHERE prefix = ?";
    $stmt = $conn->prepare($query);
    $stmt->bind_param("s", $prefix);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($row = $result->fetch_assoc()) {
        $next_number = $row['current_number'] + 1;
        $format = $row['format'];
        
        // Update the running number
        $update_query = "UPDATE running_numbers SET current_number = ? WHERE prefix = ?";
        $update_stmt = $conn->prepare($update_query);
        $update_stmt->bind_param("is", $next_number, $prefix);
        $update_stmt->execute();
        
        // Format the number
        $year = date('Y');
        $month = date('m');
        $day = date('d');
        $number = str_pad($next_number, 4, '0', STR_PAD_LEFT);
        
        $formatted_number = str_replace(
            ['{YYYY}', '{MM}', '{DD}', '{####}'],
            [$year, $month, $day, $number],
            $format
        );
        
        return $formatted_number;
    }
    
    return false;
}

// Function to update account payable amounts after adjustment
function updateAccountPayableAfterAdjustment($conn, $account_payable_id) {
    // Get total adjustments
    $adj_query = "
        SELECT 
            SUM(CASE WHEN adjustment_type = 'credit' THEN adjustment_amount ELSE 0 END) as total_credit,
            SUM(CASE WHEN adjustment_type = 'debit' THEN adjustment_amount ELSE 0 END) as total_debit
        FROM account_payable_adjustments 
        WHERE account_payable_id = ? AND approved_at IS NOT NULL
    ";
    
    $adj_stmt = $conn->prepare($adj_query);
    $adj_stmt->bind_param("i", $account_payable_id);
    $adj_stmt->execute();
    $adj_result = $adj_stmt->get_result();
    $adj_data = $adj_result->fetch_assoc();
    
    $total_credit = (float)$adj_data['total_credit'];
    $total_debit = (float)$adj_data['total_debit'];
    
    // Get original amounts and current paid amount
    $ap_query = "
        SELECT 
            (amount + vat_amount - withholding_tax) as original_net,
            paid_amount
        FROM account_payables 
        WHERE id = ?
    ";
    
    $ap_stmt = $conn->prepare($ap_query);
    $ap_stmt->bind_param("i", $account_payable_id);
    $ap_stmt->execute();
    $ap_result = $ap_stmt->get_result();
    $ap_data = $ap_result->fetch_assoc();
    
    $original_net = (float)$ap_data['original_net'];
    $paid_amount = (float)$ap_data['paid_amount'];
    
    // Calculate new net amount (credit reduces debt, debit increases debt)
    $new_net_amount = $original_net - $total_credit + $total_debit;
    $remaining_amount = $new_net_amount - $paid_amount;
    
    // Determine status
    $status = 'pending';
    if ($paid_amount >= $new_net_amount) {
        $status = 'paid';
    } elseif ($paid_amount > 0) {
        $status = 'partial_paid';
    }
    
    // Update account payable
    $update_query = "UPDATE account_payables SET net_amount = ?, remaining_amount = ?, status = ? WHERE id = ?";
    $update_stmt = $conn->prepare($update_query);
    $update_stmt->bind_param("ddsi", $new_net_amount, $remaining_amount, $status, $account_payable_id);
    $update_stmt->execute();
    
    return [
        'net_amount' => $new_net_amount, 
        'remaining_amount' => $remaining_amount, 
        'status' => $status
    ];
}

// Get request method
$method = $_SERVER['REQUEST_METHOD'];

switch ($method) {
    case 'GET':
        if (isset($_GET['action'])) {
            switch ($_GET['action']) {
                case 'list':
                    // Get all adjustments with pagination and filters
                    $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
                    $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 10;
                    $offset = ($page - 1) * $limit;
                    
                    $where_conditions = [];
                    $params = [];
                    $types = "";
                    
                    // Filter by account payable
                    if (isset($_GET['account_payable_id']) && !empty($_GET['account_payable_id'])) {
                        $where_conditions[] = "apa.account_payable_id = ?";
                        $params[] = $_GET['account_payable_id'];
                        $types .= "i";
                    }
                    
                    // Filter by adjustment type
                    if (isset($_GET['adjustment_type']) && !empty($_GET['adjustment_type'])) {
                        $where_conditions[] = "apa.adjustment_type = ?";
                        $params[] = $_GET['adjustment_type'];
                        $types .= "s";
                    }
                    
                    // Filter by approval status
                    if (isset($_GET['approved']) && $_GET['approved'] !== '') {
                        if ($_GET['approved'] === '1') {
                            $where_conditions[] = "apa.approved_at IS NOT NULL";
                        } else {
                            $where_conditions[] = "apa.approved_at IS NULL";
                        }
                    }
                    
                    // Filter by adjustment date range
                    if (isset($_GET['adjustment_date_from']) && !empty($_GET['adjustment_date_from'])) {
                        $where_conditions[] = "apa.adjustment_date >= ?";
                        $params[] = $_GET['adjustment_date_from'];
                        $types .= "s";
                    }
                    
                    if (isset($_GET['adjustment_date_to']) && !empty($_GET['adjustment_date_to'])) {
                        $where_conditions[] = "apa.adjustment_date <= ?";
                        $params[] = $_GET['adjustment_date_to'];
                        $types .= "s";
                    }
                    
                    $where_clause = !empty($where_conditions) ? "WHERE " . implode(" AND ", $where_conditions) : "";
                    
                    $query = "
                        SELECT 
                            apa.*,
                            ap.ap_number,
                            s.fullname as supplier_name,
                            s.supplier_code,
                            u1.fullname as created_by_name,
                            u2.fullname as approved_by_name
                        FROM account_payable_adjustments apa
                        LEFT JOIN account_payables ap ON apa.account_payable_id = ap.id
                        LEFT JOIN suppliers s ON ap.supplier_id = s.id
                        LEFT JOIN users u1 ON apa.created_by = u1.id
                        LEFT JOIN users u2 ON apa.approved_by = u2.id
                        {$where_clause}
                        ORDER BY apa.adjustment_date DESC, apa.created_at DESC
                        LIMIT ? OFFSET ?
                    ";
                    
                    $stmt = $conn->prepare($query);
                    $params[] = $limit;
                    $params[] = $offset;
                    $types .= "ii";
                    
                    if (!empty($params)) {
                        $stmt->bind_param($types, ...$params);
                    }
                    
                    $stmt->execute();
                    $result = $stmt->get_result();
                    $adjustments = [];
                    
                    while ($row = $result->fetch_assoc()) {
                        $adjustments[] = $row;
                    }
                    
                    // Get total count
                    $count_query = "
                        SELECT COUNT(*) as total 
                        FROM account_payable_adjustments apa
                        LEFT JOIN account_payables ap ON apa.account_payable_id = ap.id
                        LEFT JOIN suppliers s ON ap.supplier_id = s.id
                        {$where_clause}
                    ";
                    $count_stmt = $conn->prepare($count_query);
                    
                    if (!empty($where_conditions)) {
                        $count_types = substr($types, 0, -2); // Remove last 'ii' for limit and offset
                        $count_params = array_slice($params, 0, -2); // Remove last 2 params
                        $count_stmt->bind_param($count_types, ...$count_params);
                    }
                    
                    $count_stmt->execute();
                    $count_result = $count_stmt->get_result();
                    $total = $count_result->fetch_assoc()['total'];
                    
                    echo json_encode([
                        'success' => true,
                        'data' => $adjustments,
                        'total' => $total,
                        'page' => $page,
                        'limit' => $limit,
                        'total_pages' => ceil($total / $limit)
                    ]);
                    break;
                    
                case 'get':
                    // Get single adjustment
                    if (!isset($_GET['id'])) {
                        echo json_encode(['success' => false, 'message' => 'ID is required']);
                        exit;
                    }
                    
                    $id = $_GET['id'];
                    $query = "
                        SELECT 
                            apa.*,
                            ap.ap_number,
                            ap.net_amount as ap_net_amount,
                            ap.remaining_amount as ap_remaining_amount,
                            s.fullname as supplier_name,
                            s.supplier_code,
                            u1.fullname as created_by_name,
                            u2.fullname as approved_by_name
                        FROM account_payable_adjustments apa
                        LEFT JOIN account_payables ap ON apa.account_payable_id = ap.id
                        LEFT JOIN suppliers s ON ap.supplier_id = s.id
                        LEFT JOIN users u1 ON apa.created_by = u1.id
                        LEFT JOIN users u2 ON apa.approved_by = u2.id
                        WHERE apa.id = ?
                    ";
                    
                    $stmt = $conn->prepare($query);
                    $stmt->bind_param("i", $id);
                    $stmt->execute();
                    $result = $stmt->get_result();
                    
                    if ($row = $result->fetch_assoc()) {
                        echo json_encode(['success' => true, 'data' => $row]);
                    } else {
                        echo json_encode(['success' => false, 'message' => 'Adjustment not found']);
                    }
                    break;
                    
                default:
                    echo json_encode(['success' => false, 'message' => 'Invalid action']);
            }
        } else {
            echo json_encode(['success' => false, 'message' => 'Action is required']);
        }
        break;
        
    case 'POST':
        if (isset($_GET['action']) && $_GET['action'] === 'approve') {
            // Approve adjustment
            $input = json_decode(file_get_contents('php://input'), true);
            
            if (!$input || !isset($input['id'])) {
                echo json_encode(['success' => false, 'message' => 'ID is required']);
                exit;
            }
            
            $id = $input['id'];
            
            // Check if adjustment exists and is not already approved
            $check_query = "SELECT account_payable_id, approved_at FROM account_payable_adjustments WHERE id = ?";
            $check_stmt = $conn->prepare($check_query);
            $check_stmt->bind_param("i", $id);
            $check_stmt->execute();
            $check_result = $check_stmt->get_result();
            
            if (!$check_data = $check_result->fetch_assoc()) {
                echo json_encode(['success' => false, 'message' => 'Adjustment not found']);
                exit;
            }
            
            if ($check_data['approved_at']) {
                echo json_encode(['success' => false, 'message' => 'Adjustment is already approved']);
                exit;
            }
            
            $account_payable_id = $check_data['account_payable_id'];
            
            // Start transaction
            $conn->begin_transaction();
            
            try {
                // Approve adjustment
                $approve_query = "UPDATE account_payable_adjustments SET approved_by = ?, approved_at = NOW() WHERE id = ?";
                $approve_stmt = $conn->prepare($approve_query);
                $approve_stmt->bind_param("ii", $_SESSION['user_id'], $id);
                
                if (!$approve_stmt->execute()) {
                    throw new Exception('Failed to approve adjustment: ' . $conn->error);
                }
                
                // Update account payable amounts
                $update_result = updateAccountPayableAfterAdjustment($conn, $account_payable_id);
                
                $conn->commit();
                
                echo json_encode([
                    'success' => true, 
                    'message' => 'Adjustment approved successfully',
                    'account_payable_status' => $update_result
                ]);
                
            } catch (Exception $e) {
                $conn->rollback();
                echo json_encode(['success' => false, 'message' => $e->getMessage()]);
            }
            
        } else {
            // Create new adjustment
            $input = json_decode(file_get_contents('php://input'), true);
            
            if (!$input) {
                echo json_encode(['success' => false, 'message' => 'Invalid JSON input']);
                exit;
            }
            
            // Validate required fields
            $required_fields = ['account_payable_id', 'adjustment_date', 'adjustment_type', 'adjustment_amount', 'reason'];
            foreach ($required_fields as $field) {
                if (!isset($input[$field]) || empty($input[$field])) {
                    echo json_encode(['success' => false, 'message' => "Field {$field} is required"]);
                    exit;
                }
            }
            
            // Validate adjustment amount
            $adjustment_amount = (float)$input['adjustment_amount'];
            if ($adjustment_amount <= 0) {
                echo json_encode(['success' => false, 'message' => 'Adjustment amount must be greater than 0']);
                exit;
            }
            
            // Validate adjustment type
            if (!in_array($input['adjustment_type'], ['credit', 'debit'])) {
                echo json_encode(['success' => false, 'message' => 'Invalid adjustment type']);
                exit;
            }
            
            // Check if account payable exists
            $ap_query = "SELECT id FROM account_payables WHERE id = ?";
            $ap_stmt = $conn->prepare($ap_query);
            $ap_stmt->bind_param("i", $input['account_payable_id']);
            $ap_stmt->execute();
            $ap_result = $ap_stmt->get_result();
            
            if (!$ap_result->fetch_assoc()) {
                echo json_encode(['success' => false, 'message' => 'Account payable not found']);
                exit;
            }
            
            // Generate adjustment number
            $adjustment_number = generateNextNumber($conn, 'ADJ');
            if (!$adjustment_number) {
                echo json_encode(['success' => false, 'message' => 'Failed to generate adjustment number']);
                exit;
            }
            
            // Insert adjustment
            $query = "
                INSERT INTO account_payable_adjustments (
                    adjustment_number, account_payable_id, adjustment_date, adjustment_type,
                    adjustment_amount, reason, reference_document, created_by, created_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())
            ";
            
            $stmt = $conn->prepare($query);
            $stmt->bind_param(
                "sissdssi",
                $adjustment_number,
                $input['account_payable_id'],
                $input['adjustment_date'],
                $input['adjustment_type'],
                $adjustment_amount,
                $input['reason'],
                $input['reference_document'] ?? null,
                $_SESSION['user_id']
            );
            
            if ($stmt->execute()) {
                $new_id = $conn->insert_id;
                echo json_encode([
                    'success' => true, 
                    'message' => 'Adjustment created successfully (pending approval)',
                    'id' => $new_id,
                    'adjustment_number' => $adjustment_number
                ]);
            } else {
                echo json_encode(['success' => false, 'message' => 'Failed to create adjustment: ' . $conn->error]);
            }
        }
        break;
        
    case 'PUT':
        // Update adjustment (only if not approved)
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (!$input || !isset($input['id'])) {
            echo json_encode(['success' => false, 'message' => 'ID is required']);
            exit;
        }
        
        $id = $input['id'];
        
        // Check if adjustment exists and is not approved
        $check_query = "SELECT approved_at FROM account_payable_adjustments WHERE id = ?";
        $check_stmt = $conn->prepare($check_query);
        $check_stmt->bind_param("i", $id);
        $check_stmt->execute();
        $check_result = $check_stmt->get_result();
        
        if (!$check_data = $check_result->fetch_assoc()) {
            echo json_encode(['success' => false, 'message' => 'Adjustment not found']);
            exit;
        }
        
        if ($check_data['approved_at']) {
            echo json_encode(['success' => false, 'message' => 'Cannot update approved adjustment']);
            exit;
        }
        
        $set_clauses = [];
        $params = [];
        $types = "";
        
        $updatable_fields = [
            'adjustment_date' => 's',
            'adjustment_type' => 's',
            'adjustment_amount' => 'd',
            'reason' => 's',
            'reference_document' => 's'
        ];
        
        foreach ($updatable_fields as $field => $type) {
            if (isset($input[$field])) {
                $set_clauses[] = "{$field} = ?";
                $params[] = $input[$field];
                $types .= $type;
            }
        }
        
        if (empty($set_clauses)) {
            echo json_encode(['success' => false, 'message' => 'No fields to update']);
            exit;
        }
        
        // Validate adjustment amount if being updated
        if (isset($input['adjustment_amount'])) {
            $adjustment_amount = (float)$input['adjustment_amount'];
            if ($adjustment_amount <= 0) {
                echo json_encode(['success' => false, 'message' => 'Adjustment amount must be greater than 0']);
                exit;
            }
        }
        
        // Validate adjustment type if being updated
        if (isset($input['adjustment_type']) && !in_array($input['adjustment_type'], ['credit', 'debit'])) {
            echo json_encode(['success' => false, 'message' => 'Invalid adjustment type']);
            exit;
        }
        
        $params[] = $id;
        $types .= "i";
        
        $query = "UPDATE account_payable_adjustments SET " . implode(", ", $set_clauses) . " WHERE id = ?";
        $stmt = $conn->prepare($query);
        $stmt->bind_param($types, ...$params);
        
        if ($stmt->execute()) {
            echo json_encode(['success' => true, 'message' => 'Adjustment updated successfully']);
        } else {
            echo json_encode(['success' => false, 'message' => 'Failed to update adjustment: ' . $conn->error]);
        }
        break;
        
    case 'DELETE':
        // Delete adjustment (only if not approved)
        if (!isset($_GET['id'])) {
            echo json_encode(['success' => false, 'message' => 'ID is required']);
            exit;
        }
        
        $id = $_GET['id'];
        
        // Check if adjustment exists and is not approved
        $check_query = "SELECT account_payable_id, approved_at FROM account_payable_adjustments WHERE id = ?";
        $check_stmt = $conn->prepare($check_query);
        $check_stmt->bind_param("i", $id);
        $check_stmt->execute();
        $check_result = $check_stmt->get_result();
        
        if (!$check_data = $check_result->fetch_assoc()) {
            echo json_encode(['success' => false, 'message' => 'Adjustment not found']);
            exit;
        }
        
        if ($check_data['approved_at']) {
            echo json_encode(['success' => false, 'message' => 'Cannot delete approved adjustment']);
            exit;
        }
        
        // Delete adjustment
        $delete_query = "DELETE FROM account_payable_adjustments WHERE id = ?";
        $stmt = $conn->prepare($delete_query);
        $stmt->bind_param("i", $id);
        
        if ($stmt->execute()) {
            echo json_encode(['success' => true, 'message' => 'Adjustment deleted successfully']);
        } else {
            echo json_encode(['success' => false, 'message' => 'Failed to delete adjustment: ' . $conn->error]);
        }
        break;
        
    default:
        echo json_encode(['success' => false, 'message' => 'Method not allowed']);
}

$conn->close();
?>
