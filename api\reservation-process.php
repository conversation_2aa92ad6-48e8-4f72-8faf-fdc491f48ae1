<?php
include '../config.php';

// get data by $_GET['id']
if (isset($_GET['id'])) {
    $id = $_GET['id'];
    $sql = "SELECT *
            FROM reservations
            WHERE id = $id";
    $result = mysqli_query($conn, $sql);
    $data = [];

    while ($row = mysqli_fetch_assoc($result)) {
        $data[] = $row;
    }
    echo json_encode($data);
} else if (isset($_GET['customer_id'])) {
    $customer_id = $_GET['customer_id'];
    // ดึง reservations ที่มีสินค้าใน reservation_details ที่ quantity > 0 เท่านั้น
    $sql = "SELECT r.* FROM reservations r 
            INNER JOIN reservation_details rd ON r.id = rd.reservation_id
            INNER JOIN products p ON p.product_code = rd.product_code
            WHERE r.customer_id = $customer_id AND p.quantity > 0
            GROUP BY r.id";
    $result = mysqli_query($conn, $sql);
    $data = [];

    while ($row = mysqli_fetch_assoc($result)) {
        $data[] = $row;
    }
    echo json_encode($data);
    
} else if (isset($_GET['purchase_order'])) {
    $purchase_order = $_GET['purchase_order'];
    $sql = "SELECT * FROM reservations WHERE purchase_order = '$purchase_order'";
    $result = mysqli_query($conn, $sql);
    $data = [];

    while ($row = mysqli_fetch_assoc($result)) {
        $data[] = $row;
    }
    echo json_encode($data);
}