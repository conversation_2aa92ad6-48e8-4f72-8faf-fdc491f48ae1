<?php
include '../config.php';

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type');

try {
    // Get current year
    $current_year = date('Y');
    
    // Query to get monthly purchase data for current year from orders table
    $monthly_purchases_query = "
        SELECT 
            MONTH(order_date) as month,
            SUM(grand_total) as total_purchases,
            COUNT(*) as total_purchase_orders
        FROM orders 
        WHERE YEAR(order_date) = '$current_year'
        GROUP BY MONTH(order_date)
        ORDER BY MONTH(order_date)
    ";
    
    $result = mysqli_query($conn, $monthly_purchases_query);
    
    if (!$result) {
        throw new Exception('Database query failed: ' . mysqli_error($conn));
    }
    
    $monthly_data = [];
    $months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    
    // Initialize all months with zero values
    foreach ($months as $index => $month) {
        $monthly_data[] = [
            'month' => $month,
            'total_purchases' => 0,
            'total_purchase_orders' => 0,
            'combined_total' => 0
        ];
    }
    
    // Fill in purchase orders data
    while ($row = mysqli_fetch_assoc($result)) {
        $month_index = $row['month'] - 1;
        $monthly_data[$month_index]['total_purchases'] = (float)$row['total_purchases'];
        $monthly_data[$month_index]['total_purchase_orders'] = (int)$row['total_purchase_orders'];
        $monthly_data[$month_index]['combined_total'] = (float)$row['total_purchases'];
    }
    
    // Get comparison data for previous year
    $previous_year = $current_year - 1;
    $previous_year_query = "
        SELECT 
            MONTH(order_date) as month,
            SUM(grand_total) as total_purchases
        FROM orders 
        WHERE YEAR(order_date) = '$previous_year'
        GROUP BY MONTH(order_date)
        ORDER BY MONTH(order_date)
    ";
    
    $prev_result = mysqli_query($conn, $previous_year_query);
    $previous_year_data = array_fill(0, 12, 0);
    
    if ($prev_result) {
        while ($row = mysqli_fetch_assoc($prev_result)) {
            $previous_year_data[$row['month'] - 1] = (float)$row['total_purchases'];
        }
    }
    
    // Prepare response
    $response = [
        'success' => true,
        'current_year' => $current_year,
        'previous_year' => $previous_year,
        'monthly_data' => $monthly_data,
        'previous_year_data' => $previous_year_data
    ];
    
    echo json_encode($response);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}

mysqli_close($conn);
?>
