<?php
include '../config.php';
include '../auth.php';

// get data by $_GET['reservation_id']
if (isset($_GET['reservation_id'])) {
    $reservation_id = $_GET['reservation_id'];
    $sql = "SELECT d.*, p.profile_image 
            FROM reservation_details d
            INNER JOIN products p ON d.product_id = p.id
            WHERE d.reservation_id = $reservation_id";
    $result = mysqli_query($conn, $sql);
    $data = [];

    while ($row = mysqli_fetch_assoc($result)) {
        $data[] = $row;
    }
    echo json_encode($data);
}