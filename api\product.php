<?php
include '../config.php';
include '../auth.php';

// get product data by $_GET['customer_id'] with optional quotation_id
if (isset($_GET['customer_id'])) {
    $customer_id = $_GET['customer_id'];
    $quotation_id = isset($_GET['quotation_id']) ? $_GET['quotation_id'] : null;
      // Check if only count is requested
    if (isset($_GET['count_only']) && $_GET['count_only'] == '1') {
        if ($quotation_id) {
            // Count products from quotation details with customer filter
            $sql = "SELECT COUNT(DISTINCT p.id) as total 
                    FROM products p 
                    INNER JOIN quotation_details qd ON p.id = qd.product_id 
                    WHERE qd.quotation_id = '$quotation_id' AND p.customer_id = '$customer_id'";
            
            // Add search functionality for count with quotation
            if (isset($_GET['search']) && !empty($_GET['search'])) {
                $search = mysqli_real_escape_string($conn, $_GET['search']);
                $sql .= " AND (p.product_code LIKE '%$search%' OR p.product_name LIKE '%$search%')";
            }
        } else {
            // Count products by customer
            $sql = "SELECT COUNT(*) as total FROM products WHERE customer_id = '$customer_id'";
            
            // Add search functionality for count without quotation
            if (isset($_GET['search']) && !empty($_GET['search'])) {
                $search = mysqli_real_escape_string($conn, $_GET['search']);
                $sql .= " AND (product_code LIKE '%$search%' OR product_name LIKE '%$search%')";
            }
        }
        
        $result = mysqli_query($conn, $sql);
        if (!$result) {
            echo json_encode(array('error' => 'Database query failed: ' . mysqli_error($conn)));
            exit;
        }
        $row = mysqli_fetch_assoc($result);
        echo json_encode(array('total' => (int)$row['total']));
        exit;
    }    // Build main query
    if ($quotation_id) {
        // Get products from quotation details with quantity from quotation
        $sql = "SELECT p.*, qd.quantity as quotation_quantity, u.unit_name 
                FROM products p 
                INNER JOIN quotation_details qd ON p.id = qd.product_id 
                LEFT JOIN units u ON p.unit_id = u.id
                WHERE qd.quotation_id = '$quotation_id' AND p.customer_id = '$customer_id'";
    } else {
        // Get products by customer
        $sql = "SELECT p.*, u.unit_name 
                FROM products p 
                LEFT JOIN units u ON p.unit_id = u.id
                WHERE p.customer_id = '$customer_id'";
    }
    
    // Add search functionality
    if (isset($_GET['search']) && !empty($_GET['search'])) {
        $search = mysqli_real_escape_string($conn, $_GET['search']);
        if ($quotation_id) {
            $sql .= " AND (p.product_code LIKE '%$search%' OR p.product_name LIKE '%$search%')";
        } else {
            $sql .= " AND (product_code LIKE '%$search%' OR product_name LIKE '%$search%')";
        }
    }    // Add ORDER BY clause
    $sql .= " ORDER BY id ASC";
    
    // Add pagination if limit and offset are provided
    if (isset($_GET['limit']) && isset($_GET['offset'])) {
        $limit = (int)$_GET['limit'];
        $offset = (int)$_GET['offset'];
        $sql .= " LIMIT $limit OFFSET $offset";
    }
    
    $result = mysqli_query($conn, $sql);
    if (!$result) {
        echo json_encode(array('error' => 'Database query failed: ' . mysqli_error($conn)));
        exit;
    }
    $data = array();
    while ($row = mysqli_fetch_assoc($result)) {
        $data[] = $row;
    }
    echo json_encode($data);
}

// get product data by $_GET['reservation_id'] with optional quotation_id
elseif (isset($_GET['reservation_id'])) {
    $reservation_id = $_GET['reservation_id'];
    
    // Check if only count is requested
    if (isset($_GET['count_only']) && $_GET['count_only'] == '1') {
        $sql = "SELECT COUNT(DISTINCT p.id) as total 
                FROM products p 
                INNER JOIN reservation_details rd ON p.product_code = rd.product_code
                LEFT JOIN units u ON p.unit_id = u.id
                WHERE rd.reservation_id = '$reservation_id and p.quantity > 0'";
        
        // Add search functionality for count
        if (isset($_GET['search']) && !empty($_GET['search'])) {
            $search = mysqli_real_escape_string($conn, $_GET['search']);
            $sql .= " AND (p.product_code LIKE '%$search%' OR p.product_name LIKE '%$search%')";
        }        
        $result = mysqli_query($conn, $sql);
        if (!$result) {
            echo json_encode(array('error' => 'Database query failed: ' . mysqli_error($conn)));
            exit;
        }
        $row = mysqli_fetch_assoc($result);
        echo json_encode(array('total' => (int)$row['total']));
        exit;
    }
      // ดึงสินค้าทั้งหมดที่เกี่ยวข้องกับ reservation_id จาก products
    $sql = "SELECT p.*, rd.quantity as reservation_quantity, u.unit_name FROM products p 
            INNER JOIN reservation_details rd ON p.product_code = rd.product_code
            LEFT JOIN units u ON p.unit_id = u.id
            WHERE rd.reservation_id = '$reservation_id AND p.quantity > 0'";
    
    // Add search functionality
    if (isset($_GET['search']) && !empty($_GET['search'])) {
        $search = mysqli_real_escape_string($conn, $_GET['search']);
        $sql .= " AND (p.product_code LIKE '%$search%' OR p.product_name LIKE '%$search%')";
    }

    // Add ORDER BY clause
    $sql .= " ORDER BY p.id ASC";
    
    // Add pagination if limit and offset are provided
    if (isset($_GET['limit']) && isset($_GET['offset'])) {
        $limit = (int)$_GET['limit'];
        $offset = (int)$_GET['offset'];
        $sql .= " LIMIT $limit OFFSET $offset";
    }
      $result = mysqli_query($conn, $sql);
    if (!$result) {
        echo json_encode(array('error' => 'Database query failed: ' . mysqli_error($conn)));
        exit;
    }
    $data = array();
    while ($row = mysqli_fetch_assoc($result)) {
        $data[] = $row;
    }
    echo json_encode($data);
}

// get product data by $_GET['quotation_id ']
elseif (isset($_GET['quotation_id'])) {
    $quotation_id = $_GET['quotation_id'];
    $sql2 = "SELECT * FROM quotation_details WHERE quotation_id = $quotation_id ORDER BY id DESC";
    $result = mysqli_query($conn, $sql2);
    $data = array();
    while ($row = mysqli_fetch_assoc($result)) {
        $data[] = $row;
    }
    echo json_encode($data);
} else {
    echo json_encode(array('status' => 'error', 'message' => 'Invalid request'));
}















