<?php
include '../config.php';

// get data by $_GET['id']
if (isset($_GET['id'])) {
    $id = $_GET['id'];
    $sql = "SELECT *
            FROM orders
            WHERE id = $id";
    $result = mysqli_query($conn, $sql);
    $data = [];

    while ($row = mysqli_fetch_assoc($result)) {
        $data[] = $row;
    }
    echo json_encode($data);
}
// get data by $_GET['supplier_id']
if (isset($_GET['supplier_id'])) {
    $supplier_id = $_GET['supplier_id'];
    $sql = "SELECT o.* FROM orders o
            LEFT JOIN order_details od ON o.id = od.order_id
            LEFT JOIN products_orders p ON od.product_code = p.product_code
            WHERE o.supplier_id = $supplier_id 
            GROUP BY o.id";
    
    $result = mysqli_query($conn, $sql);
    $data = [];

    while ($row = mysqli_fetch_assoc($result)) {
        $data[] = $row;
    }
    echo json_encode($data);
}
// quatation_no
elseif (isset($_GET['quatation_no'])) {
    $quatation_no = $_GET['quatation_no'];
    $sql = "SELECT * FROM orders WHERE quatation_no = '$quatation_no'";
    $result = mysqli_query($conn, $sql);
    $data = [];

    while ($row = mysqli_fetch_assoc($result)) {
        $data[] = $row;
    }
    echo json_encode($data);
}
