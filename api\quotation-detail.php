<?php
include '../config.php';

// get customer data by $_GET['id']
if (isset($_GET['quotation_id'])) {
    $quotation_id = $_GET['quotation_id'];
    $sql = "SELECT d.*, p.profile_image
            FROM quotation_details d
            INNER JOIN products p ON d.product_id=p.id
            WHERE quotation_id = $quotation_id";
    $result = mysqli_query($conn, $sql);
    $data = [];

    while ($row = mysqli_fetch_assoc($result)) {
        $data[] = $row;
    }
    echo json_encode($data);
}