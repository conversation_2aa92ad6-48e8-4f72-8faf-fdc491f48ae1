<?php
include '../config.php';
include '../auth.php';

// Set content type to JSON
header('Content-Type: application/json');

// Function to generate next number
function generateNextNumber($conn, $prefix) {
    $query = "SELECT current_number, format FROM running_numbers WHERE prefix = ?";
    $stmt = $conn->prepare($query);
    $stmt->bind_param("s", $prefix);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($row = $result->fetch_assoc()) {
        $next_number = $row['current_number'] + 1;
        $format = $row['format'];
        
        // Update the running number
        $update_query = "UPDATE running_numbers SET current_number = ? WHERE prefix = ?";
        $update_stmt = $conn->prepare($update_query);
        $update_stmt->bind_param("is", $next_number, $prefix);
        $update_stmt->execute();
        
        // Format the number
        $year = date('Y');
        $month = date('m');
        $day = date('d');
        $number = str_pad($next_number, 4, '0', STR_PAD_LEFT);
        
        $formatted_number = str_replace(
            ['{YYYY}', '{MM}', '{DD}', '{####}'],
            [$year, $month, $day, $number],
            $format
        );
        
        return $formatted_number;
    }
    
    return false;
}

// Function to update account payable status and amounts
function updateAccountPayableStatus($conn, $account_payable_id) {
    // Get total paid amount
    $paid_query = "SELECT COALESCE(SUM(payment_amount), 0) as total_paid FROM account_payable_payments WHERE account_payable_id = ?";
    $paid_stmt = $conn->prepare($paid_query);
    $paid_stmt->bind_param("i", $account_payable_id);
    $paid_stmt->execute();
    $paid_result = $paid_stmt->get_result();
    $total_paid = $paid_result->fetch_assoc()['total_paid'];
    
    // Get account payable info
    $ap_query = "SELECT net_amount, due_date FROM account_payables WHERE id = ?";
    $ap_stmt = $conn->prepare($ap_query);
    $ap_stmt->bind_param("i", $account_payable_id);
    $ap_stmt->execute();
    $ap_result = $ap_stmt->get_result();
    $ap_data = $ap_result->fetch_assoc();
    
    $net_amount = $ap_data['net_amount'];
    $due_date = $ap_data['due_date'];
    $remaining_amount = $net_amount - $total_paid;
    
    // Determine status
    $status = 'pending';
    if ($total_paid >= $net_amount) {
        $status = 'paid';
    } elseif ($total_paid > 0) {
        $status = 'partial_paid';
    } elseif (date('Y-m-d') > $due_date) {
        $status = 'overdue';
    }
    
    // Update account payable
    $update_query = "UPDATE account_payables SET paid_amount = ?, remaining_amount = ?, status = ? WHERE id = ?";
    $update_stmt = $conn->prepare($update_query);
    $update_stmt->bind_param("ddsi", $total_paid, $remaining_amount, $status, $account_payable_id);
    $update_stmt->execute();
    
    return ['paid_amount' => $total_paid, 'remaining_amount' => $remaining_amount, 'status' => $status];
}

// Get request method
$method = $_SERVER['REQUEST_METHOD'];

switch ($method) {
    case 'GET':
        if (isset($_GET['action'])) {
            switch ($_GET['action']) {
                case 'list':
                    // Get all payments with pagination and filters
                    $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
                    $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 10;
                    $offset = ($page - 1) * $limit;
                    
                    $where_conditions = [];
                    $params = [];
                    $types = "";
                    
                    // Filter by account payable
                    if (isset($_GET['account_payable_id']) && !empty($_GET['account_payable_id'])) {
                        $where_conditions[] = "app.account_payable_id = ?";
                        $params[] = $_GET['account_payable_id'];
                        $types .= "i";
                    }
                    
                    // Filter by payment date range
                    if (isset($_GET['payment_date_from']) && !empty($_GET['payment_date_from'])) {
                        $where_conditions[] = "app.payment_date >= ?";
                        $params[] = $_GET['payment_date_from'];
                        $types .= "s";
                    }
                    
                    if (isset($_GET['payment_date_to']) && !empty($_GET['payment_date_to'])) {
                        $where_conditions[] = "app.payment_date <= ?";
                        $params[] = $_GET['payment_date_to'];
                        $types .= "s";
                    }
                    
                    // Filter by payment method
                    if (isset($_GET['payment_method']) && !empty($_GET['payment_method'])) {
                        $where_conditions[] = "app.payment_method = ?";
                        $params[] = $_GET['payment_method'];
                        $types .= "s";
                    }
                    
                    $where_clause = !empty($where_conditions) ? "WHERE " . implode(" AND ", $where_conditions) : "";
                    
                    $query = "
                        SELECT 
                            app.*,
                            ap.ap_number,
                            s.fullname as supplier_name,
                            s.supplier_code,
                            u.fullname as created_by_name
                        FROM account_payable_payments app
                        LEFT JOIN account_payables ap ON app.account_payable_id = ap.id
                        LEFT JOIN suppliers s ON ap.supplier_id = s.id
                        LEFT JOIN users u ON app.created_by = u.id
                        {$where_clause}
                        ORDER BY app.payment_date DESC, app.created_at DESC
                        LIMIT ? OFFSET ?
                    ";
                    
                    $stmt = $conn->prepare($query);
                    $params[] = $limit;
                    $params[] = $offset;
                    $types .= "ii";
                    
                    if (!empty($params)) {
                        $stmt->bind_param($types, ...$params);
                    }
                    
                    $stmt->execute();
                    $result = $stmt->get_result();
                    $payments = [];
                    
                    while ($row = $result->fetch_assoc()) {
                        $payments[] = $row;
                    }
                    
                    // Get total count
                    $count_query = "
                        SELECT COUNT(*) as total 
                        FROM account_payable_payments app
                        LEFT JOIN account_payables ap ON app.account_payable_id = ap.id
                        LEFT JOIN suppliers s ON ap.supplier_id = s.id
                        {$where_clause}
                    ";
                    $count_stmt = $conn->prepare($count_query);
                    
                    if (!empty($where_conditions)) {
                        $count_types = substr($types, 0, -2); // Remove last 'ii' for limit and offset
                        $count_params = array_slice($params, 0, -2); // Remove last 2 params
                        $count_stmt->bind_param($count_types, ...$count_params);
                    }
                    
                    $count_stmt->execute();
                    $count_result = $count_stmt->get_result();
                    $total = $count_result->fetch_assoc()['total'];
                    
                    echo json_encode([
                        'success' => true,
                        'data' => $payments,
                        'total' => $total,
                        'page' => $page,
                        'limit' => $limit,
                        'total_pages' => ceil($total / $limit)
                    ]);
                    break;
                    
                case 'get':
                    // Get single payment
                    if (!isset($_GET['id'])) {
                        echo json_encode(['success' => false, 'message' => 'ID is required']);
                        exit;
                    }
                    
                    $id = $_GET['id'];
                    $query = "
                        SELECT 
                            app.*,
                            ap.ap_number,
                            ap.net_amount as ap_net_amount,
                            ap.remaining_amount as ap_remaining_amount,
                            s.fullname as supplier_name,
                            s.supplier_code,
                            u.fullname as created_by_name
                        FROM account_payable_payments app
                        LEFT JOIN account_payables ap ON app.account_payable_id = ap.id
                        LEFT JOIN suppliers s ON ap.supplier_id = s.id
                        LEFT JOIN users u ON app.created_by = u.id
                        WHERE app.id = ?
                    ";
                    
                    $stmt = $conn->prepare($query);
                    $stmt->bind_param("i", $id);
                    $stmt->execute();
                    $result = $stmt->get_result();
                    
                    if ($row = $result->fetch_assoc()) {
                        echo json_encode(['success' => true, 'data' => $row]);
                    } else {
                        echo json_encode(['success' => false, 'message' => 'Payment not found']);
                    }
                    break;
                    
                default:
                    echo json_encode(['success' => false, 'message' => 'Invalid action']);
            }
        } else {
            echo json_encode(['success' => false, 'message' => 'Action is required']);
        }
        break;
        
    case 'POST':
        // Create new payment
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (!$input) {
            echo json_encode(['success' => false, 'message' => 'Invalid JSON input']);
            exit;
        }
        
        // Validate required fields
        $required_fields = ['account_payable_id', 'payment_date', 'payment_amount', 'payment_method'];
        foreach ($required_fields as $field) {
            if (!isset($input[$field]) || empty($input[$field])) {
                echo json_encode(['success' => false, 'message' => "Field {$field} is required"]);
                exit;
            }
        }
        
        // Validate payment amount
        $payment_amount = (float)$input['payment_amount'];
        if ($payment_amount <= 0) {
            echo json_encode(['success' => false, 'message' => 'Payment amount must be greater than 0']);
            exit;
        }
        
        // Check if account payable exists and get remaining amount
        $ap_query = "SELECT remaining_amount, status FROM account_payables WHERE id = ?";
        $ap_stmt = $conn->prepare($ap_query);
        $ap_stmt->bind_param("i", $input['account_payable_id']);
        $ap_stmt->execute();
        $ap_result = $ap_stmt->get_result();
        
        if (!$ap_data = $ap_result->fetch_assoc()) {
            echo json_encode(['success' => false, 'message' => 'Account payable not found']);
            exit;
        }
        
        if ($ap_data['status'] === 'paid') {
            echo json_encode(['success' => false, 'message' => 'Account payable is already fully paid']);
            exit;
        }
        
        if ($payment_amount > $ap_data['remaining_amount']) {
            echo json_encode(['success' => false, 'message' => 'Payment amount exceeds remaining amount']);
            exit;
        }
        
        // Generate payment number
        $payment_number = generateNextNumber($conn, 'PM');
        if (!$payment_number) {
            echo json_encode(['success' => false, 'message' => 'Failed to generate payment number']);
            exit;
        }
        
        // Start transaction
        $conn->begin_transaction();
        
        try {
            // Insert payment
            $query = "
                INSERT INTO account_payable_payments (
                    payment_number, account_payable_id, payment_date, payment_amount,
                    payment_method, bank_account, cheque_number, transaction_ref,
                    description, created_by, created_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
            ";
            
            $stmt = $conn->prepare($query);
            $stmt->bind_param(
                "sisdsssssi",
                $payment_number,
                $input['account_payable_id'],
                $input['payment_date'],
                $payment_amount,
                $input['payment_method'],
                $input['bank_account'] ?? null,
                $input['cheque_number'] ?? null,
                $input['transaction_ref'] ?? null,
                $input['description'] ?? null,
                $_SESSION['user_id']
            );
            
            if (!$stmt->execute()) {
                throw new Exception('Failed to create payment: ' . $conn->error);
            }
            
            $new_id = $conn->insert_id;
            
            // Update account payable status and amounts
            $update_result = updateAccountPayableStatus($conn, $input['account_payable_id']);
            
            $conn->commit();
            
            echo json_encode([
                'success' => true, 
                'message' => 'Payment created successfully',
                'id' => $new_id,
                'payment_number' => $payment_number,
                'account_payable_status' => $update_result
            ]);
            
        } catch (Exception $e) {
            $conn->rollback();
            echo json_encode(['success' => false, 'message' => $e->getMessage()]);
        }
        break;
        
    case 'PUT':
        // Update payment
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (!$input || !isset($input['id'])) {
            echo json_encode(['success' => false, 'message' => 'ID is required']);
            exit;
        }
        
        $id = $input['id'];
        
        // Get current payment info
        $current_query = "SELECT account_payable_id FROM account_payable_payments WHERE id = ?";
        $current_stmt = $conn->prepare($current_query);
        $current_stmt->bind_param("i", $id);
        $current_stmt->execute();
        $current_result = $current_stmt->get_result();
        
        if (!$current_data = $current_result->fetch_assoc()) {
            echo json_encode(['success' => false, 'message' => 'Payment not found']);
            exit;
        }
        
        $account_payable_id = $current_data['account_payable_id'];
        
        // Start transaction
        $conn->begin_transaction();
        
        try {
            $set_clauses = [];
            $params = [];
            $types = "";
            
            $updatable_fields = [
                'payment_date' => 's',
                'payment_amount' => 'd',
                'payment_method' => 's',
                'bank_account' => 's',
                'cheque_number' => 's',
                'transaction_ref' => 's',
                'description' => 's'
            ];
            
            foreach ($updatable_fields as $field => $type) {
                if (isset($input[$field])) {
                    $set_clauses[] = "{$field} = ?";
                    $params[] = $input[$field];
                    $types .= $type;
                }
            }
            
            if (empty($set_clauses)) {
                throw new Exception('No fields to update');
            }
            
            // Validate payment amount if being updated
            if (isset($input['payment_amount'])) {
                $payment_amount = (float)$input['payment_amount'];
                if ($payment_amount <= 0) {
                    throw new Exception('Payment amount must be greater than 0');
                }
            }
            
            $params[] = $id;
            $types .= "i";
            
            $query = "UPDATE account_payable_payments SET " . implode(", ", $set_clauses) . " WHERE id = ?";
            $stmt = $conn->prepare($query);
            $stmt->bind_param($types, ...$params);
            
            if (!$stmt->execute()) {
                throw new Exception('Failed to update payment: ' . $conn->error);
            }
            
            // Update account payable status and amounts
            $update_result = updateAccountPayableStatus($conn, $account_payable_id);
            
            $conn->commit();
            
            echo json_encode([
                'success' => true, 
                'message' => 'Payment updated successfully',
                'account_payable_status' => $update_result
            ]);
            
        } catch (Exception $e) {
            $conn->rollback();
            echo json_encode(['success' => false, 'message' => $e->getMessage()]);
        }
        break;
        
    case 'DELETE':
        // Delete payment
        if (!isset($_GET['id'])) {
            echo json_encode(['success' => false, 'message' => 'ID is required']);
            exit;
        }
        
        $id = $_GET['id'];
        
        // Get account payable ID before deletion
        $ap_query = "SELECT account_payable_id FROM account_payable_payments WHERE id = ?";
        $ap_stmt = $conn->prepare($ap_query);
        $ap_stmt->bind_param("i", $id);
        $ap_stmt->execute();
        $ap_result = $ap_stmt->get_result();
        
        if (!$ap_data = $ap_result->fetch_assoc()) {
            echo json_encode(['success' => false, 'message' => 'Payment not found']);
            exit;
        }
        
        $account_payable_id = $ap_data['account_payable_id'];
        
        // Start transaction
        $conn->begin_transaction();
        
        try {
            // Delete payment
            $delete_query = "DELETE FROM account_payable_payments WHERE id = ?";
            $stmt = $conn->prepare($delete_query);
            $stmt->bind_param("i", $id);
            
            if (!$stmt->execute()) {
                throw new Exception('Failed to delete payment: ' . $conn->error);
            }
            
            // Update account payable status and amounts
            $update_result = updateAccountPayableStatus($conn, $account_payable_id);
            
            $conn->commit();
            
            echo json_encode([
                'success' => true, 
                'message' => 'Payment deleted successfully',
                'account_payable_status' => $update_result
            ]);
            
        } catch (Exception $e) {
            $conn->rollback();
            echo json_encode(['success' => false, 'message' => $e->getMessage()]);
        }
        break;
        
    default:
        echo json_encode(['success' => false, 'message' => 'Method not allowed']);
}

$conn->close();
?>
