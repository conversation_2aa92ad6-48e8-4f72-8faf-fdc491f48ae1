<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

include '../config.php';

function sendResponse($success, $data = null, $message = '', $code = 200) {
    http_response_code($code);
    echo json_encode([
        'success' => $success,
        'data' => $data,
        'message' => $message
    ]);
    exit;
}

function validateRequired($data, $fields) {
    foreach ($fields as $field) {
        if (!isset($data[$field]) || empty(trim($data[$field]))) {
            return "กรุณากรอก $field";
        }
    }
    return null;
}

function getNextDocumentNumber($conn, $prefix = 'BAC') {
    $year = date('Y');
    $month = date('m');
    
    // Get running number for this month
    $query = "SELECT running_value FROM running_numbers 
              WHERE prefix = ? AND year = ? AND month = ? 
              FOR UPDATE";
    $stmt = mysqli_prepare($conn, $query);
    mysqli_stmt_bind_param($stmt, 'sii', $prefix, $year, $month);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
    
    if ($row = mysqli_fetch_assoc($result)) {
        $newValue = $row['running_value'] + 1;
        $updateQuery = "UPDATE running_numbers 
                       SET running_value = ? 
                       WHERE prefix = ? AND year = ? AND month = ?";
        $updateStmt = mysqli_prepare($conn, $updateQuery);
        mysqli_stmt_bind_param($updateStmt, 'isii', $newValue, $prefix, $year, $month);
        mysqli_stmt_execute($updateStmt);
    } else {
        $newValue = 1;
        $insertQuery = "INSERT INTO running_numbers (prefix, year, month, running_value) 
                       VALUES (?, ?, ?, ?)";
        $insertStmt = mysqli_prepare($conn, $insertQuery);
        mysqli_stmt_bind_param($insertStmt, 'siii', $prefix, $year, $month, $newValue);
        mysqli_stmt_execute($insertStmt);
    }
    
    return $prefix . $year . $month . str_pad($newValue, 4, '0', STR_PAD_LEFT);
}

$method = $_SERVER['REQUEST_METHOD'];
$action = $_GET['action'] ?? '';

try {
    mysqli_begin_transaction($conn);
    
    switch ($method) {
        case 'GET':
            if ($action === 'list') {
                $page = (int)($_GET['page'] ?? 1);
                $limit = (int)($_GET['limit'] ?? 10);
                $offset = ($page - 1) * $limit;
                $keyword = $_GET['keyword'] ?? '';
                $supplierId = $_GET['supplier_id'] ?? '';
                $dateFrom = $_GET['date_from'] ?? '';
                $dateTo = $_GET['date_to'] ?? '';
                
                $whereConditions = ['1=1'];
                $params = [];
                $types = '';
                
                if ($keyword) {
                    $whereConditions[] = "(ba.bill_no LIKE ? OR ba.description LIKE ? OR s.fullname LIKE ?)";
                    $params[] = "%$keyword%";
                    $params[] = "%$keyword%";
                    $params[] = "%$keyword%";
                    $types .= 'sss';
                }
                
                if ($supplierId) {
                    $whereConditions[] = "ba.supplier_id = ?";
                    $params[] = $supplierId;
                    $types .= 'i';
                }
                
                if ($dateFrom) {
                    $whereConditions[] = "ba.bill_date >= ?";
                    $params[] = $dateFrom;
                    $types .= 's';
                }
                
                if ($dateTo) {
                    $whereConditions[] = "ba.bill_date <= ?";
                    $params[] = $dateTo;
                    $types .= 's';
                }
                
                $whereClause = implode(' AND ', $whereConditions);
                
                $query = "SELECT ba.*, s.fullname as supplier_name, s.supplier_code
                         FROM bill_acceptances ba
                         LEFT JOIN suppliers s ON ba.supplier_id = s.id
                         WHERE $whereClause
                         ORDER BY ba.created_at DESC
                         LIMIT ? OFFSET ?";
                
                $params[] = $limit;
                $params[] = $offset;
                $types .= 'ii';
                
                $stmt = mysqli_prepare($conn, $query);
                if ($types) {
                    mysqli_stmt_bind_param($stmt, $types, ...$params);
                }
                mysqli_stmt_execute($stmt);
                $result = mysqli_stmt_get_result($stmt);
                
                $data = [];
                while ($row = mysqli_fetch_assoc($result)) {
                    $data[] = $row;
                }
                
                // Get total count
                $countParams = array_slice($params, 0, -2); // Remove limit and offset
                $countTypes = substr($types, 0, -2);
                
                $countQuery = "SELECT COUNT(*) as total
                              FROM bill_acceptances ba
                              LEFT JOIN suppliers s ON ba.supplier_id = s.id
                              WHERE $whereClause";
                
                $countStmt = mysqli_prepare($conn, $countQuery);
                if ($countTypes) {
                    mysqli_stmt_bind_param($countStmt, $countTypes, ...$countParams);
                }
                mysqli_stmt_execute($countStmt);
                $countResult = mysqli_stmt_get_result($countStmt);
                $totalCount = mysqli_fetch_assoc($countResult)['total'];
                
                $pagination = [
                    'current_page' => $page,
                    'total_pages' => ceil($totalCount / $limit),
                    'total_records' => $totalCount,
                    'limit' => $limit
                ];
                
                sendResponse(true, $data, '', 200);
                
            } elseif ($action === 'get' && isset($_GET['id'])) {
                $id = (int)$_GET['id'];
                
                $query = "SELECT ba.*, s.fullname as supplier_name, s.supplier_code
                         FROM bill_acceptances ba
                         LEFT JOIN suppliers s ON ba.supplier_id = s.id
                         WHERE ba.id = ?";
                
                $stmt = mysqli_prepare($conn, $query);
                mysqli_stmt_bind_param($stmt, 'i', $id);
                mysqli_stmt_execute($stmt);
                $result = mysqli_stmt_get_result($stmt);
                
                if ($row = mysqli_fetch_assoc($result)) {
                    // Get bill items
                    $itemsQuery = "SELECT * FROM bill_acceptance_items WHERE bill_acceptance_id = ?";
                    $itemsStmt = mysqli_prepare($conn, $itemsQuery);
                    mysqli_stmt_bind_param($itemsStmt, 'i', $id);
                    mysqli_stmt_execute($itemsStmt);
                    $itemsResult = mysqli_stmt_get_result($itemsStmt);
                    
                    $items = [];
                    while ($item = mysqli_fetch_assoc($itemsResult)) {
                        $items[] = $item;
                    }
                    
                    $row['items'] = $items;
                    sendResponse(true, $row);
                } else {
                    sendResponse(false, null, 'ไม่พบข้อมูล', 404);
                }
            }
            break;
            
        case 'POST':
            $data = $_POST;
            
            $validation = validateRequired($data, ['supplier_id', 'bill_date', 'bill_no', 'bill_amount']);
            if ($validation) {
                sendResponse(false, null, $validation, 400);
            }
            
            // Generate document number
            $documentNo = getNextDocumentNumber($conn, 'BAC');
            
            // Insert bill acceptance
            $query = "INSERT INTO bill_acceptances 
                     (document_no, supplier_id, bill_date, bill_no, bill_amount, vat_amount, withholding_amount, description, created_at, updated_at)
                     VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())";
            
            $stmt = mysqli_prepare($conn, $query);
            mysqli_stmt_bind_param($stmt, 'sissddds', 
                $documentNo,
                $data['supplier_id'],
                $data['bill_date'],
                $data['bill_no'],
                $data['bill_amount'],
                $data['vat_amount'] ?? 0,
                $data['withholding_amount'] ?? 0,
                $data['description'] ?? ''
            );
            
            if (!mysqli_stmt_execute($stmt)) {
                throw new Exception('เกิดข้อผิดพลาดในการบันทึกใบรับวางบิล');
            }
            
            $billAcceptanceId = mysqli_insert_id($conn);
            
            // Insert bill items if provided
            if (isset($data['items'])) {
                $items = json_decode($data['items'], true);
                if ($items && is_array($items)) {
                    $itemQuery = "INSERT INTO bill_acceptance_items 
                                 (bill_acceptance_id, description, quantity, unit_price, total_amount)
                                 VALUES (?, ?, ?, ?, ?)";
                    $itemStmt = mysqli_prepare($conn, $itemQuery);
                    
                    foreach ($items as $item) {
                        if (!empty($item['description'])) {
                            mysqli_stmt_bind_param($itemStmt, 'isddd',
                                $billAcceptanceId,
                                $item['description'],
                                $item['quantity'] ?? 0,
                                $item['unit_price'] ?? 0,
                                $item['total_amount'] ?? 0
                            );
                            mysqli_stmt_execute($itemStmt);
                        }
                    }
                }
            }
            
            // Create account payable if requested
            if (isset($data['create_account_payable']) && $data['create_account_payable']) {
                // Get supplier credit days
                $supplierQuery = "SELECT credit_day FROM suppliers WHERE id = ?";
                $supplierStmt = mysqli_prepare($conn, $supplierQuery);
                mysqli_stmt_bind_param($supplierStmt, 'i', $data['supplier_id']);
                mysqli_stmt_execute($supplierStmt);
                $supplierResult = mysqli_stmt_get_result($supplierStmt);
                $supplier = mysqli_fetch_assoc($supplierResult);
                
                $creditDays = $supplier['credit_day'] ?? 30;
                $dueDate = date('Y-m-d', strtotime($data['bill_date'] . " +$creditDays days"));
                
                // Generate AP document number
                $apDocumentNo = getNextDocumentNumber($conn, 'AP');
                
                $apQuery = "INSERT INTO account_payables 
                           (document_no, supplier_id, document_date, due_date, ref_document_no, ref_document_date, 
                            amount, vat_amount, withholding_amount, remaining_amount, description, status, created_at, updated_at)
                           VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'unpaid', NOW(), NOW())";
                
                $apStmt = mysqli_prepare($conn, $apQuery);
                mysqli_stmt_bind_param($apStmt, 'sissssddds',
                    $apDocumentNo,
                    $data['supplier_id'],
                    $data['bill_date'],
                    $dueDate,
                    $data['bill_no'],
                    $data['bill_date'],
                    $data['bill_amount'],
                    $data['vat_amount'] ?? 0,
                    $data['withholding_amount'] ?? 0,
                    $data['bill_amount'],
                    'จากใบรับวางบิล: ' . $data['bill_no']
                );
                
                if (!mysqli_stmt_execute($apStmt)) {
                    throw new Exception('เกิดข้อผิดพลาดในการสร้างบัญชีเจ้าหนี้');
                }
            }
            
            mysqli_commit($conn);
            sendResponse(true, ['id' => $billAcceptanceId, 'document_no' => $documentNo], 'บันทึกใบรับวางบิลเรียบร้อย');
            break;
            
        case 'PUT':
            $data = $_POST;
            $id = (int)($data['id'] ?? 0);
            
            if (!$id) {
                sendResponse(false, null, 'ไม่พบ ID ที่ต้องการแก้ไข', 400);
            }
            
            $validation = validateRequired($data, ['supplier_id', 'bill_date', 'bill_no', 'bill_amount']);
            if ($validation) {
                sendResponse(false, null, $validation, 400);
            }
            
            $query = "UPDATE bill_acceptances 
                     SET supplier_id = ?, bill_date = ?, bill_no = ?, bill_amount = ?, 
                         vat_amount = ?, withholding_amount = ?, description = ?, updated_at = NOW()
                     WHERE id = ?";
            
            $stmt = mysqli_prepare($conn, $query);
            mysqli_stmt_bind_param($stmt, 'issdddsi',
                $data['supplier_id'],
                $data['bill_date'],
                $data['bill_no'],
                $data['bill_amount'],
                $data['vat_amount'] ?? 0,
                $data['withholding_amount'] ?? 0,
                $data['description'] ?? '',
                $id
            );
            
            if (mysqli_stmt_execute($stmt)) {
                mysqli_commit($conn);
                sendResponse(true, null, 'แก้ไขข้อมูลเรียบร้อย');
            } else {
                throw new Exception('เกิดข้อผิดพลาดในการแก้ไขข้อมูล');
            }
            break;
            
        case 'DELETE':
            $id = (int)($_GET['id'] ?? 0);
            
            if (!$id) {
                sendResponse(false, null, 'ไม่พบ ID ที่ต้องการลบ', 400);
            }
            
            // Delete items first
            $deleteItemsQuery = "DELETE FROM bill_acceptance_items WHERE bill_acceptance_id = ?";
            $deleteItemsStmt = mysqli_prepare($conn, $deleteItemsQuery);
            mysqli_stmt_bind_param($deleteItemsStmt, 'i', $id);
            mysqli_stmt_execute($deleteItemsStmt);
            
            // Delete bill acceptance
            $query = "DELETE FROM bill_acceptances WHERE id = ?";
            $stmt = mysqli_prepare($conn, $query);
            mysqli_stmt_bind_param($stmt, 'i', $id);
            
            if (mysqli_stmt_execute($stmt)) {
                mysqli_commit($conn);
                sendResponse(true, null, 'ลบข้อมูลเรียบร้อย');
            } else {
                throw new Exception('เกิดข้อผิดพลาดในการลบข้อมูล');
            }
            break;
            
        default:
            sendResponse(false, null, 'Method not allowed', 405);
    }
    
} catch (Exception $e) {
    mysqli_rollback($conn);
    sendResponse(false, null, $e->getMessage(), 500);
}
?>
