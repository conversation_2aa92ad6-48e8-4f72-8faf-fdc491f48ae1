<?php
include '../config.php';
include '../auth.php';

// get reservation_details data by $_GET['customer_id']
if (isset($_GET['customer_id'])) {
    $customer_id = $_GET['customer_id'];
    $reservation_id = isset($_GET['reservation_id']) ? (int)$_GET['reservation_id'] : 0;
    
    // Handle count only request for pagination
    if (isset($_GET['count_only']) && $_GET['count_only'] == 1) {        $count_sql = "SELECT COUNT(*) as total FROM reservation_details rd
                     INNER JOIN products p ON rd.product_id = p.id
                     LEFT JOIN units u ON p.unit_id = u.id
                     WHERE rd.reservation_id = $reservation_id";
        
        // Add search functionality
        if (isset($_GET['search']) && !empty(trim($_GET['search']))) {
            $search = mysqli_real_escape_string($conn, trim($_GET['search']));
            $count_sql .= " AND (p.product_code LIKE '%$search%' OR p.product_name LIKE '%$search%')";
        }
        
        $count_result = mysqli_query($conn, $count_sql);
        $count_row = mysqli_fetch_assoc($count_result);
        echo json_encode(['total' => (int)$count_row['total']]);
        exit;
    }
      // Main query with pagination
    $sql = "SELECT rd.*, p.product_code, p.product_name, p.unit_id, p.price, p.profile_image, u.unit_name FROM reservation_details rd
            INNER JOIN products p ON rd.product_id = p.id
            LEFT JOIN units u ON p.unit_id = u.id
            WHERE rd.reservation_id = $reservation_id";
    
    // Add search functionality
    if (isset($_GET['search']) && !empty(trim($_GET['search']))) {
        $search = mysqli_real_escape_string($conn, trim($_GET['search']));
        $sql .= " AND (p.product_code LIKE '%$search%' OR p.product_name LIKE '%$search%')";
    }
    
    // Add ORDER BY clause
    $sql .= " ORDER BY rd.id ASC";
    
    // Add pagination if limit and offset are provided
    if (isset($_GET['limit']) && isset($_GET['offset'])) {
        $limit = (int)$_GET['limit'];
        $offset = (int)$_GET['offset'];
        $sql .= " LIMIT $limit OFFSET $offset";
    }
    
    $result = mysqli_query($conn, $sql);
    if (!$result) {
        echo json_encode(['error' => 'Database query failed: ' . mysqli_error($conn)]);
        exit;
    }
    
    $data = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $data[] = $row;
    }
    echo json_encode($data);
}
