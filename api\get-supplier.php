<?php
include '../config.php';
include '../auth.php';

// get supplier data by $_GET['id']
if (isset($_GET['id'])) {
    $id = $_GET['id'];
    $sql = "SELECT * FROM suppliers WHERE id = $id";
    $result = mysqli_query($conn, $sql);
    $data = mysqli_fetch_assoc($result);
    echo json_encode($data);
}
// get all suppliers
else {
    // get all suppliers
    $sql = "SELECT * FROM suppliers ORDER BY fullname";
    $result = mysqli_query($conn, $sql);
    
    if ($result) {
        $data = array();
        while ($row = mysqli_fetch_assoc($result)) {
            $data[] = $row;
        }
        echo json_encode($data);
    } else {
        echo json_encode(['error' => 'Database query failed: ' . mysqli_error($conn)]);
    }
}
?>


