<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

include '../config.php';

function sendResponse($success, $data = null, $message = '', $code = 200) {
    http_response_code($code);
    echo json_encode([
        'success' => $success,
        'data' => $data,
        'message' => $message
    ]);
    exit;
}

$method = $_SERVER['REQUEST_METHOD'];
$id = $_GET['id'] ?? null;

try {
    switch ($method) {
        case 'GET':
            if ($id) {
                // Get single supplier
                $query = "SELECT * FROM suppliers WHERE id = ?";
                $stmt = mysqli_prepare($conn, $query);
                mysqli_stmt_bind_param($stmt, 'i', $id);
                mysqli_stmt_execute($stmt);
                $result = mysqli_stmt_get_result($stmt);
                
                if ($row = mysqli_fetch_assoc($result)) {
                    sendResponse(true, $row);
                } else {
                    sendResponse(false, null, 'ไม่พบข้อมูลเจ้าหนี้', 404);
                }
            } else {
                // Get all suppliers
                $page = (int)($_GET['page'] ?? 1);
                $limit = (int)($_GET['limit'] ?? 100);
                $offset = ($page - 1) * $limit;
                $keyword = $_GET['keyword'] ?? '';
                
                $whereClause = '1=1';
                $params = [];
                $types = '';
                
                if ($keyword) {
                    $whereClause .= " AND (supplier_code LIKE ? OR fullname LIKE ? OR email LIKE ?)";
                    $params[] = "%$keyword%";
                    $params[] = "%$keyword%";
                    $params[] = "%$keyword%";
                    $types = 'sss';
                }
                
                $query = "SELECT * FROM suppliers WHERE $whereClause ORDER BY supplier_code LIMIT ? OFFSET ?";
                $params[] = $limit;
                $params[] = $offset;
                $types .= 'ii';
                
                $stmt = mysqli_prepare($conn, $query);
                if ($types) {
                    mysqli_stmt_bind_param($stmt, $types, ...$params);
                }
                mysqli_stmt_execute($stmt);
                $result = mysqli_stmt_get_result($stmt);
                
                $data = [];
                while ($row = mysqli_fetch_assoc($result)) {
                    $data[] = $row;
                }
                
                sendResponse(true, $data);
            }
            break;
            
        case 'POST':
            $data = $_POST;
            
            // Validate required fields
            $required = ['supplier_code', 'fullname'];
            foreach ($required as $field) {
                if (!isset($data[$field]) || empty(trim($data[$field]))) {
                    sendResponse(false, null, "กรุณากรอก $field", 400);
                }
            }
            
            // Check if supplier_code already exists
            $checkQuery = "SELECT id FROM suppliers WHERE supplier_code = ?";
            $checkStmt = mysqli_prepare($conn, $checkQuery);
            mysqli_stmt_bind_param($checkStmt, 's', $data['supplier_code']);
            mysqli_stmt_execute($checkStmt);
            $checkResult = mysqli_stmt_get_result($checkStmt);
            
            if (mysqli_fetch_assoc($checkResult)) {
                sendResponse(false, null, 'รหัสเจ้าหนี้นี้มีอยู่แล้ว', 400);
            }
            
            $query = "INSERT INTO suppliers 
                     (supplier_code, email, fullname, tel, address, tax_no, credit_day, credit_limit, 
                      payment_type, vat_type, withholding_type, created_at, updated_at)
                     VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())";
            
            $stmt = mysqli_prepare($conn, $query);
            mysqli_stmt_bind_param($stmt, 'ssssssissss',
                $data['supplier_code'],
                $data['email'] ?? '',
                $data['fullname'],
                $data['tel'] ?? '',
                $data['address'] ?? '',
                $data['tax_no'] ?? '',
                $data['credit_day'] ?? 30,
                $data['credit_limit'] ?? 0,
                $data['payment_type'] ?? 'cash',
                $data['vat_type'] ?? 'exclude',
                $data['withholding_type'] ?? 'none'
            );
            
            if (mysqli_stmt_execute($stmt)) {
                $newId = mysqli_insert_id($conn);
                sendResponse(true, ['id' => $newId], 'เพิ่มเจ้าหนี้เรียบร้อย');
            } else {
                sendResponse(false, null, 'เกิดข้อผิดพลาดในการเพิ่มเจ้าหนี้', 500);
            }
            break;
            
        case 'PUT':
            $data = $_POST;
            $updateId = (int)($data['id'] ?? 0);
            
            if (!$updateId) {
                sendResponse(false, null, 'ไม่พบ ID ที่ต้องการแก้ไข', 400);
            }
            
            // Validate required fields
            $required = ['supplier_code', 'fullname'];
            foreach ($required as $field) {
                if (!isset($data[$field]) || empty(trim($data[$field]))) {
                    sendResponse(false, null, "กรุณากรอก $field", 400);
                }
            }
            
            // Check if supplier_code already exists (exclude current record)
            $checkQuery = "SELECT id FROM suppliers WHERE supplier_code = ? AND id != ?";
            $checkStmt = mysqli_prepare($conn, $checkQuery);
            mysqli_stmt_bind_param($checkStmt, 'si', $data['supplier_code'], $updateId);
            mysqli_stmt_execute($checkStmt);
            $checkResult = mysqli_stmt_get_result($checkStmt);
            
            if (mysqli_fetch_assoc($checkResult)) {
                sendResponse(false, null, 'รหัสเจ้าหนี้นี้มีอยู่แล้ว', 400);
            }
            
            $query = "UPDATE suppliers 
                     SET supplier_code = ?, email = ?, fullname = ?, tel = ?, address = ?, 
                         tax_no = ?, credit_day = ?, credit_limit = ?, payment_type = ?, 
                         vat_type = ?, withholding_type = ?, updated_at = NOW()
                     WHERE id = ?";
            
            $stmt = mysqli_prepare($conn, $query);
            mysqli_stmt_bind_param($stmt, 'ssssssissssi',
                $data['supplier_code'],
                $data['email'] ?? '',
                $data['fullname'],
                $data['tel'] ?? '',
                $data['address'] ?? '',
                $data['tax_no'] ?? '',
                $data['credit_day'] ?? 30,
                $data['credit_limit'] ?? 0,
                $data['payment_type'] ?? 'cash',
                $data['vat_type'] ?? 'exclude',
                $data['withholding_type'] ?? 'none',
                $updateId
            );
            
            if (mysqli_stmt_execute($stmt)) {
                sendResponse(true, null, 'แก้ไขเจ้าหนี้เรียบร้อย');
            } else {
                sendResponse(false, null, 'เกิดข้อผิดพลาดในการแก้ไขเจ้าหนี้', 500);
            }
            break;
            
        case 'DELETE':
            $deleteId = (int)($_GET['id'] ?? 0);
            
            if (!$deleteId) {
                sendResponse(false, null, 'ไม่พบ ID ที่ต้องการลบ', 400);
            }
            
            // Check if supplier has account payables
            $checkQuery = "SELECT COUNT(*) as count FROM account_payables WHERE supplier_id = ?";
            $checkStmt = mysqli_prepare($conn, $checkQuery);
            mysqli_stmt_bind_param($checkStmt, 'i', $deleteId);
            mysqli_stmt_execute($checkStmt);
            $checkResult = mysqli_stmt_get_result($checkStmt);
            $count = mysqli_fetch_assoc($checkResult)['count'];
            
            if ($count > 0) {
                sendResponse(false, null, 'ไม่สามารถลบเจ้าหนี้ได้ เนื่องจากมีบัญชีเจ้าหนี้อยู่', 400);
            }
            
            $query = "DELETE FROM suppliers WHERE id = ?";
            $stmt = mysqli_prepare($conn, $query);
            mysqli_stmt_bind_param($stmt, 'i', $deleteId);
            
            if (mysqli_stmt_execute($stmt)) {
                sendResponse(true, null, 'ลบเจ้าหนี้เรียบร้อย');
            } else {
                sendResponse(false, null, 'เกิดข้อผิดพลาดในการลบเจ้าหนี้', 500);
            }
            break;
            
        default:
            sendResponse(false, null, 'Method not allowed', 405);
    }
    
} catch (Exception $e) {
    sendResponse(false, null, $e->getMessage(), 500);
}
?>
