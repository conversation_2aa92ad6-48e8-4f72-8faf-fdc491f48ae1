<?php
header('Content-Type: application/json');
include '../config.php';
include '../auth.php';

try {
    // ตรวจสอบว่ามี parameter id หรือไม่ (สำหรับดึงข้อมูลความแข็งรายเดียว)
    if (isset($_GET['id']) && !empty($_GET['id'])) {
        $hardness_id = intval($_GET['id']);
        $query = "SELECT id, hardness_code, hardness_name, description, status 
                  FROM hardnesses 
                  WHERE id = $hardness_id";
        $result = mysqli_query($conn, $query);
        
        if ($result && mysqli_num_rows($result) > 0) {
            $hardness = mysqli_fetch_assoc($result);
            echo json_encode($hardness);
        } else {
            echo json_encode(['error' => 'ไม่พบข้อมูลความแข็ง']);
        }
    } else {
        // ดึงข้อมูลความแข็งทั้งหมด
        $query = "SELECT id, hardness_code, hardness_name, description, status 
                  FROM hardnesses 
                  WHERE status = 'active' 
                  ORDER BY hardness_code ASC, hardness_name ASC";
        $result = mysqli_query($conn, $query);
        
        $hardnesses = [];
        if ($result) {
            while ($row = mysqli_fetch_assoc($result)) {
                $hardnesses[] = $row;
            }
        }
        
        echo json_encode($hardnesses);
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => 'เกิดข้อผิดพลาดในการดึงข้อมูล: ' . $e->getMessage()]);
}
?>
