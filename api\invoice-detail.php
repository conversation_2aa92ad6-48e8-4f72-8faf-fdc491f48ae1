<?php
include '../config.php';

// get data by $_GET['id']
if (isset($_GET['invoice_id'])) {
    $invoice_id = $_GET['invoice_id'];
    $sql = "SELECT d.*, p.profile_image
            FROM invoice_details d
            INNER JOIN products p ON d.product_id=p.id
            WHERE invoice_id = $invoice_id";
    $result = mysqli_query($conn, $sql);
    $data = [];

    while ($row = mysqli_fetch_assoc($result)) {
        $data[] = $row;
    }
    echo json_encode($data);
}