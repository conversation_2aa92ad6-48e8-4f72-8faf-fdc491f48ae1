<?php
include '../config.php';
include '../auth.php';

// get data by $_GET['id']
if (isset($_GET['id'])) {
    $id = $_GET['id'];
    $sql = "SELECT *
            FROM requisitions
            WHERE id = $id";
    $result = mysqli_query($conn, $sql);
    $data = [];

    while ($row = mysqli_fetch_assoc($result)) {
        $data[] = $row;
    }
    echo json_encode($data);
} else if (isset($_GET['supplier_id'])) {
    $supplier_id = $_GET['supplier_id'];
    $sql = "SELECT * FROM requisitions WHERE supplier_id = $supplier_id ";
    $result = mysqli_query($conn, $sql);
    $data = [];

    while ($row = mysqli_fetch_assoc($result)) {
        $data[] = $row;
    }
    echo json_encode($data);
    
} else if (isset($_GET['quotations'])) {
    $quotations = $_GET['quotations'];
    $sql = "SELECT * FROM requisitions WHERE quotations = '$quotations'";
    $result = mysqli_query($conn, $sql);
    $data = [];

    while ($row = mysqli_fetch_assoc($result)) {
        $data[] = $row;
    }
    echo json_encode($data);
}
