<?php
include '../config.php';
include '../auth.php';

// get data by $_GET['order_id']
if (isset($_GET['order_id'])) {
    $order_id = intval($_GET['order_id']); // Sanitize input
    $sql = "SELECT d.*, p.profile_image
            FROM order_details d
            LEFT JOIN products p ON d.product_id = p.id
            WHERE d.order_id = $order_id
            ORDER BY d.id";
    $result = mysqli_query($conn, $sql);
    
    if (!$result) {
        echo json_encode(['error' => 'Database query failed: ' . mysqli_error($conn)]);
        exit;
    }
    
    $data = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $data[] = $row;
    }
    echo json_encode($data);
} else {
    echo json_encode(['error' => 'order_id parameter is required']);
}