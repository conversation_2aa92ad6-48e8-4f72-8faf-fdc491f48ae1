<?php
include '../config.php';
include '../auth.php';

// get product_order data by $_GET['supplier_id']
if (isset($_GET['supplier_id'])) {
    $supplier_id = $_GET['supplier_id'];
    
    // Handle count only request for pagination
    if (isset($_GET['count_only']) && $_GET['count_only'] == 1) {
        $count_sql = "SELECT COUNT(*) as total FROM products_orders WHERE supplier_id = '$supplier_id'";
        
        // Add search functionality
        if (isset($_GET['search']) && !empty(trim($_GET['search']))) {
            $search = mysqli_real_escape_string($conn, trim($_GET['search']));
            $count_sql .= " AND (product_code LIKE '%$search%' OR product_name LIKE '%$search%')";
        }
        
        $count_result = mysqli_query($conn, $count_sql);
        $count_row = mysqli_fetch_assoc($count_result);
        echo json_encode(['total' => (int)$count_row['total']]);
        exit;
    }
    
    // Main query with pagination
    $sql = "SELECT po.*, u.unit_name 
            FROM products_orders po
            LEFT JOIN units u ON po.unit_id = u.id  
            WHERE po.supplier_id = '$supplier_id'";
    
    // Add search functionality
    if (isset($_GET['search']) && !empty(trim($_GET['search']))) {
        $search = mysqli_real_escape_string($conn, trim($_GET['search']));
        $sql .= " AND (product_code LIKE '%$search%' OR product_name LIKE '%$search%')";
    }
    
    // Add ORDER BY DESC
    $sql .= " ORDER BY id ASC";
    
    // Add pagination
    $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 10;
    $offset = isset($_GET['offset']) ? (int)$_GET['offset'] : 0;
    $sql .= " LIMIT $limit OFFSET $offset";
    
    $result = mysqli_query($conn, $sql);
    $data = array();
    while ($row = mysqli_fetch_assoc($result)) {
        $data[] = $row;
    }
    echo json_encode($data);
}

// get products_orders data by $_GET['requisition_id ']
elseif (isset($_GET['requisition_id'])) {
    $requisition_id = $_GET['requisition_id'];
    $sql = "SELECT * FROM requisition_details WHERE requisition_id = $requisition_id 
    ORDER BY id DESC";
    $result = mysqli_query($conn, $sql);
    $data = array();
    while ($row = mysqli_fetch_assoc($result)) {
        $data[] = $row;
    }
    echo json_encode($data);
}

// get product_order data by $_GET['order_id']
elseif (isset($_GET['order_id'])) {
    $order_id = $_GET['order_id'];
    
    // Handle count only request for pagination
    if (isset($_GET['count_only']) && $_GET['count_only'] == 1) {
        $count_sql = "SELECT COUNT(*) as total FROM order_details WHERE order_id = $order_id";
        
        // Add search functionality
        if (isset($_GET['search']) && !empty(trim($_GET['search']))) {
            $search = mysqli_real_escape_string($conn, trim($_GET['search']));
            $count_sql .= " AND (product_code LIKE '%$search%' OR product_name LIKE '%$search%')";
        }
        
        $count_result = mysqli_query($conn, $count_sql);
        $count_row = mysqli_fetch_assoc($count_result);
        echo json_encode(['total' => (int)$count_row['total']]);
        exit;
    }
    
    // Main query with pagination
    $sql = "SELECT * FROM order_details WHERE order_id = $order_id";
    
    // Add search functionality
    if (isset($_GET['search']) && !empty(trim($_GET['search']))) {
        $search = mysqli_real_escape_string($conn, trim($_GET['search']));
        $sql .= " AND (product_code LIKE '%$search%' OR product_name LIKE '%$search%')";
    }
    
    // Add ORDER BY DESC
    $sql .= " ORDER BY id ASC";
    
    // Add pagination
    $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 10;
    $offset = isset($_GET['offset']) ? (int)$_GET['offset'] : 0;
    $sql .= " LIMIT $limit OFFSET $offset";
    
    $result = mysqli_query($conn, $sql);
    $data = array();
    while ($row = mysqli_fetch_assoc($result)) {
        $data[] = $row;
    }
    echo json_encode($data);
}

