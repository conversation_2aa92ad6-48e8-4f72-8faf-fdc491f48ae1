<?php
include '../config.php';
include '../auth.php';

// get data by $_GET['material_order_id']
if (isset($_GET['material_order_id'])) {
    $material_order_id = $_GET['material_order_id'];
    $sql = "SELECT * FROM material_order_details
            WHERE material_order_id = ?";
    $stmt = mysqli_prepare($conn, $sql);
    mysqli_stmt_bind_param($stmt, "i", $material_order_id);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
    $data = [];

    while ($row = mysqli_fetch_assoc($result)) {
        $data[] = $row;
    }
    echo json_encode($data);
}
