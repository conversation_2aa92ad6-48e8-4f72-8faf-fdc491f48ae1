<?php
include '../config.php';
include '../auth.php';

// Set content type to JSON
header('Content-Type: application/json');

// Get request method
$method = $_SERVER['REQUEST_METHOD'];

if ($method !== 'GET') {
    echo json_encode(['success' => false, 'message' => 'Only GET method allowed']);
    exit;
}

if (!isset($_GET['report_type'])) {
    echo json_encode(['success' => false, 'message' => 'Report type is required']);
    exit;
}

$report_type = $_GET['report_type'];

switch ($report_type) {
    case 'outstanding_payables':
        // รายงานหนี้สินคงค้าง
        $where_conditions = [];
        $params = [];
        $types = "";
        
        // Filter by supplier
        if (isset($_GET['supplier_id']) && !empty($_GET['supplier_id'])) {
            $where_conditions[] = "ap.supplier_id = ?";
            $params[] = $_GET['supplier_id'];
            $types .= "i";
        }
        
        // Filter by status
        if (isset($_GET['status']) && !empty($_GET['status'])) {
            $where_conditions[] = "ap.status = ?";
            $params[] = $_GET['status'];
            $types .= "s";
        } else {
            // Default: only show unpaid/partial paid
            $where_conditions[] = "ap.status IN ('pending', 'partial_paid', 'overdue')";
        }
        
        // Filter by due date range
        if (isset($_GET['due_date_from']) && !empty($_GET['due_date_from'])) {
            $where_conditions[] = "ap.due_date >= ?";
            $params[] = $_GET['due_date_from'];
            $types .= "s";
        }
        
        if (isset($_GET['due_date_to']) && !empty($_GET['due_date_to'])) {
            $where_conditions[] = "ap.due_date <= ?";
            $params[] = $_GET['due_date_to'];
            $types .= "s";
        }
        
        $where_clause = !empty($where_conditions) ? "WHERE " . implode(" AND ", $where_conditions) : "";
        
        $query = "
            SELECT 
                ap.id,
                ap.ap_number,
                ap.bill_date,
                ap.due_date,
                ap.invoice_number,
                ap.net_amount,
                ap.paid_amount,
                ap.remaining_amount,
                ap.status,
                DATEDIFF(CURDATE(), ap.due_date) as overdue_days,
                s.fullname as supplier_name,
                s.supplier_code,
                s.tel as supplier_tel,
                s.contact_name
            FROM account_payables ap
            LEFT JOIN suppliers s ON ap.supplier_id = s.id
            {$where_clause}
            ORDER BY ap.due_date ASC, s.fullname ASC
        ";
        
        $stmt = $conn->prepare($query);
        if (!empty($params)) {
            $stmt->bind_param($types, ...$params);
        }
        
        $stmt->execute();
        $result = $stmt->get_result();
        $data = [];
        $total_amount = 0;
        $total_remaining = 0;
        
        while ($row = $result->fetch_assoc()) {
            $data[] = $row;
            $total_amount += $row['net_amount'];
            $total_remaining += $row['remaining_amount'];
        }
        
        echo json_encode([
            'success' => true,
            'data' => $data,
            'summary' => [
                'total_records' => count($data),
                'total_amount' => $total_amount,
                'total_remaining' => $total_remaining
            ]
        ]);
        break;
        
    case 'aging_report':
        // รายงานอายุหนี้
        $supplier_filter = "";
        $params = [];
        $types = "";
        
        if (isset($_GET['supplier_id']) && !empty($_GET['supplier_id'])) {
            $supplier_filter = "WHERE ap.supplier_id = ?";
            $params[] = $_GET['supplier_id'];
            $types = "i";
        }
        
        $query = "
            SELECT 
                s.id as supplier_id,
                s.fullname as supplier_name,
                s.supplier_code,
                SUM(ap.remaining_amount) as total_outstanding,
                SUM(CASE 
                    WHEN DATEDIFF(CURDATE(), ap.due_date) <= 0 THEN ap.remaining_amount 
                    ELSE 0 
                END) as current_amount,
                SUM(CASE 
                    WHEN DATEDIFF(CURDATE(), ap.due_date) BETWEEN 1 AND 30 THEN ap.remaining_amount 
                    ELSE 0 
                END) as days_1_30,
                SUM(CASE 
                    WHEN DATEDIFF(CURDATE(), ap.due_date) BETWEEN 31 AND 60 THEN ap.remaining_amount 
                    ELSE 0 
                END) as days_31_60,
                SUM(CASE 
                    WHEN DATEDIFF(CURDATE(), ap.due_date) BETWEEN 61 AND 90 THEN ap.remaining_amount 
                    ELSE 0 
                END) as days_61_90,
                SUM(CASE 
                    WHEN DATEDIFF(CURDATE(), ap.due_date) > 90 THEN ap.remaining_amount 
                    ELSE 0 
                END) as days_over_90
            FROM suppliers s
            LEFT JOIN account_payables ap ON s.id = ap.supplier_id 
                AND ap.status IN ('pending', 'partial_paid', 'overdue')
                AND ap.remaining_amount > 0
            {$supplier_filter}
            GROUP BY s.id, s.fullname, s.supplier_code
            HAVING total_outstanding > 0
            ORDER BY total_outstanding DESC
        ";
        
        $stmt = $conn->prepare($query);
        if (!empty($params)) {
            $stmt->bind_param($types, ...$params);
        }
        
        $stmt->execute();
        $result = $stmt->get_result();
        $data = [];
        $summary = [
            'total_outstanding' => 0,
            'current_amount' => 0,
            'days_1_30' => 0,
            'days_31_60' => 0,
            'days_61_90' => 0,
            'days_over_90' => 0
        ];
        
        while ($row = $result->fetch_assoc()) {
            $data[] = $row;
            $summary['total_outstanding'] += $row['total_outstanding'];
            $summary['current_amount'] += $row['current_amount'];
            $summary['days_1_30'] += $row['days_1_30'];
            $summary['days_31_60'] += $row['days_31_60'];
            $summary['days_61_90'] += $row['days_61_90'];
            $summary['days_over_90'] += $row['days_over_90'];
        }
        
        echo json_encode([
            'success' => true,
            'data' => $data,
            'summary' => $summary
        ]);
        break;
        
    case 'payment_history':
        // รายงานประวัติการชำระเงิน
        $where_conditions = [];
        $params = [];
        $types = "";
        
        // Filter by supplier
        if (isset($_GET['supplier_id']) && !empty($_GET['supplier_id'])) {
            $where_conditions[] = "ap.supplier_id = ?";
            $params[] = $_GET['supplier_id'];
            $types .= "i";
        }
        
        // Filter by payment date range
        if (isset($_GET['payment_date_from']) && !empty($_GET['payment_date_from'])) {
            $where_conditions[] = "app.payment_date >= ?";
            $params[] = $_GET['payment_date_from'];
            $types .= "s";
        }
        
        if (isset($_GET['payment_date_to']) && !empty($_GET['payment_date_to'])) {
            $where_conditions[] = "app.payment_date <= ?";
            $params[] = $_GET['payment_date_to'];
            $types .= "s";
        }
        
        // Filter by payment method
        if (isset($_GET['payment_method']) && !empty($_GET['payment_method'])) {
            $where_conditions[] = "app.payment_method = ?";
            $params[] = $_GET['payment_method'];
            $types .= "s";
        }
        
        $where_clause = !empty($where_conditions) ? "WHERE " . implode(" AND ", $where_conditions) : "";
        
        $query = "
            SELECT 
                app.id,
                app.payment_number,
                app.payment_date,
                app.payment_amount,
                app.payment_method,
                app.bank_account,
                app.cheque_number,
                app.transaction_ref,
                app.description,
                ap.ap_number,
                ap.bill_date,
                ap.invoice_number,
                s.fullname as supplier_name,
                s.supplier_code,
                u.fullname as created_by_name
            FROM account_payable_payments app
            LEFT JOIN account_payables ap ON app.account_payable_id = ap.id
            LEFT JOIN suppliers s ON ap.supplier_id = s.id
            LEFT JOIN users u ON app.created_by = u.id
            {$where_clause}
            ORDER BY app.payment_date DESC, app.created_at DESC
        ";
        
        $stmt = $conn->prepare($query);
        if (!empty($params)) {
            $stmt->bind_param($types, ...$params);
        }
        
        $stmt->execute();
        $result = $stmt->get_result();
        $data = [];
        $total_payments = 0;
        
        while ($row = $result->fetch_assoc()) {
            $data[] = $row;
            $total_payments += $row['payment_amount'];
        }
        
        echo json_encode([
            'success' => true,
            'data' => $data,
            'summary' => [
                'total_records' => count($data),
                'total_payments' => $total_payments
            ]
        ]);
        break;
        
    case 'supplier_summary':
        // รายงานสรุปยอดตามเจ้าหนี้
        $where_conditions = ["ap.status != 'cancelled'"];
        $params = [];
        $types = "";
        
        // Filter by supplier
        if (isset($_GET['supplier_id']) && !empty($_GET['supplier_id'])) {
            $where_conditions[] = "s.id = ?";
            $params[] = $_GET['supplier_id'];
            $types .= "i";
        }
        
        // Filter by date range
        if (isset($_GET['date_from']) && !empty($_GET['date_from'])) {
            $where_conditions[] = "ap.bill_date >= ?";
            $params[] = $_GET['date_from'];
            $types .= "s";
        }
        
        if (isset($_GET['date_to']) && !empty($_GET['date_to'])) {
            $where_conditions[] = "ap.bill_date <= ?";
            $params[] = $_GET['date_to'];
            $types .= "s";
        }
        
        $where_clause = "WHERE " . implode(" AND ", $where_conditions);
        
        $query = "
            SELECT 
                s.id as supplier_id,
                s.fullname as supplier_name,
                s.supplier_code,
                s.tel as supplier_tel,
                s.contact_name,
                COUNT(ap.id) as total_bills,
                SUM(ap.net_amount) as total_amount,
                SUM(ap.paid_amount) as total_paid,
                SUM(ap.remaining_amount) as total_remaining,
                SUM(CASE WHEN ap.status = 'paid' THEN 1 ELSE 0 END) as paid_bills,
                SUM(CASE WHEN ap.status = 'overdue' THEN 1 ELSE 0 END) as overdue_bills,
                SUM(CASE WHEN ap.status = 'overdue' THEN ap.remaining_amount ELSE 0 END) as overdue_amount,
                AVG(ap.credit_day) as avg_credit_days,
                MAX(ap.bill_date) as last_bill_date
            FROM suppliers s
            LEFT JOIN account_payables ap ON s.id = ap.supplier_id
            {$where_clause}
            GROUP BY s.id, s.fullname, s.supplier_code, s.tel, s.contact_name
            HAVING total_bills > 0
            ORDER BY total_amount DESC
        ";
        
        $stmt = $conn->prepare($query);
        if (!empty($params)) {
            $stmt->bind_param($types, ...$params);
        }
        
        $stmt->execute();
        $result = $stmt->get_result();
        $data = [];
        $summary = [
            'total_suppliers' => 0,
            'total_bills' => 0,
            'total_amount' => 0,
            'total_paid' => 0,
            'total_remaining' => 0
        ];
        
        while ($row = $result->fetch_assoc()) {
            // Calculate payment percentage
            $row['payment_percentage'] = $row['total_amount'] > 0 ? 
                round(($row['total_paid'] / $row['total_amount']) * 100, 2) : 0;
            
            $data[] = $row;
            $summary['total_suppliers']++;
            $summary['total_bills'] += $row['total_bills'];
            $summary['total_amount'] += $row['total_amount'];
            $summary['total_paid'] += $row['total_paid'];
            $summary['total_remaining'] += $row['total_remaining'];
        }
        
        echo json_encode([
            'success' => true,
            'data' => $data,
            'summary' => $summary
        ]);
        break;
        
    case 'monthly_summary':
        // รายงานสรุปรายเดือน
        $year = isset($_GET['year']) ? $_GET['year'] : date('Y');
        
        $query = "
            SELECT 
                MONTH(ap.bill_date) as month,
                YEAR(ap.bill_date) as year,
                COUNT(ap.id) as total_bills,
                SUM(ap.net_amount) as total_amount,
                SUM(ap.paid_amount) as total_paid,
                SUM(ap.remaining_amount) as total_remaining,
                SUM(CASE WHEN ap.status = 'paid' THEN 1 ELSE 0 END) as paid_bills,
                SUM(CASE WHEN ap.status = 'overdue' THEN 1 ELSE 0 END) as overdue_bills
            FROM account_payables ap
            WHERE YEAR(ap.bill_date) = ? AND ap.status != 'cancelled'
            GROUP BY YEAR(ap.bill_date), MONTH(ap.bill_date)
            ORDER BY YEAR(ap.bill_date), MONTH(ap.bill_date)
        ";
        
        $stmt = $conn->prepare($query);
        $stmt->bind_param("i", $year);
        $stmt->execute();
        $result = $stmt->get_result();
        
        $data = [];
        $monthly_data = [];
        
        // Initialize all 12 months with zero values
        for ($i = 1; $i <= 12; $i++) {
            $monthly_data[$i] = [
                'month' => $i,
                'month_name' => date('F', mktime(0, 0, 0, $i, 1)),
                'year' => $year,
                'total_bills' => 0,
                'total_amount' => 0,
                'total_paid' => 0,
                'total_remaining' => 0,
                'paid_bills' => 0,
                'overdue_bills' => 0,
                'payment_percentage' => 0
            ];
        }
        
        // Fill with actual data
        while ($row = $result->fetch_assoc()) {
            $month = $row['month'];
            $monthly_data[$month] = array_merge($monthly_data[$month], $row);
            $monthly_data[$month]['month_name'] = date('F', mktime(0, 0, 0, $month, 1));
            $monthly_data[$month]['payment_percentage'] = $row['total_amount'] > 0 ? 
                round(($row['total_paid'] / $row['total_amount']) * 100, 2) : 0;
        }
        
        $data = array_values($monthly_data);
        
        // Calculate yearly summary
        $yearly_summary = [
            'year' => $year,
            'total_bills' => array_sum(array_column($data, 'total_bills')),
            'total_amount' => array_sum(array_column($data, 'total_amount')),
            'total_paid' => array_sum(array_column($data, 'total_paid')),
            'total_remaining' => array_sum(array_column($data, 'total_remaining')),
            'paid_bills' => array_sum(array_column($data, 'paid_bills')),
            'overdue_bills' => array_sum(array_column($data, 'overdue_bills'))
        ];
        
        $yearly_summary['payment_percentage'] = $yearly_summary['total_amount'] > 0 ? 
            round(($yearly_summary['total_paid'] / $yearly_summary['total_amount']) * 100, 2) : 0;
        
        echo json_encode([
            'success' => true,
            'data' => $data,
            'summary' => $yearly_summary
        ]);
        break;
        
    case 'dashboard_summary':
        // รายงานสรุปสำหรับหน้า Dashboard
        $summary_query = "
            SELECT 
                COUNT(*) as total_payables,
                SUM(net_amount) as total_amount,
                SUM(remaining_amount) as total_remaining,
                SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending_count,
                SUM(CASE WHEN status = 'partial_paid' THEN 1 ELSE 0 END) as partial_paid_count,
                SUM(CASE WHEN status = 'paid' THEN 1 ELSE 0 END) as paid_count,
                SUM(CASE WHEN status = 'overdue' THEN 1 ELSE 0 END) as overdue_count,
                SUM(CASE WHEN status = 'overdue' THEN remaining_amount ELSE 0 END) as overdue_amount,
                SUM(CASE WHEN due_date BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 7 DAY) AND status != 'paid' THEN remaining_amount ELSE 0 END) as due_within_week,
                SUM(CASE WHEN due_date BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 30 DAY) AND status != 'paid' THEN remaining_amount ELSE 0 END) as due_within_month
            FROM account_payables
            WHERE status != 'cancelled'
        ";
        
        $result = mysqli_query($conn, $summary_query);
        $summary = mysqli_fetch_assoc($result);
        
        // Get recent payments
        $recent_payments_query = "
            SELECT 
                app.payment_number,
                app.payment_date,
                app.payment_amount,
                ap.ap_number,
                s.fullname as supplier_name
            FROM account_payable_payments app
            LEFT JOIN account_payables ap ON app.account_payable_id = ap.id
            LEFT JOIN suppliers s ON ap.supplier_id = s.id
            ORDER BY app.created_at DESC
            LIMIT 5
        ";
        
        $recent_payments_result = mysqli_query($conn, $recent_payments_query);
        $recent_payments = [];
        while ($row = mysqli_fetch_assoc($recent_payments_result)) {
            $recent_payments[] = $row;
        }
        
        // Get upcoming due dates
        $upcoming_due_query = "
            SELECT 
                ap.ap_number,
                ap.due_date,
                ap.remaining_amount,
                s.fullname as supplier_name,
                DATEDIFF(ap.due_date, CURDATE()) as days_until_due
            FROM account_payables ap
            LEFT JOIN suppliers s ON ap.supplier_id = s.id
            WHERE ap.status IN ('pending', 'partial_paid') 
                AND ap.due_date BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 30 DAY)
            ORDER BY ap.due_date ASC
            LIMIT 10
        ";
        
        $upcoming_due_result = mysqli_query($conn, $upcoming_due_query);
        $upcoming_due = [];
        while ($row = mysqli_fetch_assoc($upcoming_due_result)) {
            $upcoming_due[] = $row;
        }
        
        echo json_encode([
            'success' => true,
            'summary' => $summary,
            'recent_payments' => $recent_payments,
            'upcoming_due' => $upcoming_due
        ]);
        break;
        
    default:
        echo json_encode(['success' => false, 'message' => 'Invalid report type']);
}

$conn->close();
?>
