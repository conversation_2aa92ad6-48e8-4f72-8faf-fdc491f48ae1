<?php
include '../config.php';

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type');

try {
    // Get current year
    $current_year = date('Y');
    
    // Query to get monthly sales data for current year from invoices table
    $monthly_sales_query = "
        SELECT 
            MONTH(document_date) as month,
            MONTHNAME(document_date) as month_name,
            SUM(grand_total) as total_sales,
            COUNT(*) as total_orders
        FROM invoices 
        WHERE YEAR(document_date) = '$current_year'
        GROUP BY MONTH(document_date), MONTHNAME(document_date)
        ORDER BY MONTH(document_date)
    ";
    
    // Query to get monthly reservations data for current year
    $monthly_reservations_query = "
        SELECT 
            MONTH(document_date) as month,
            MONTHNAME(document_date) as month_name,
            SUM(grand_total) as total_reservations,
            COUNT(*) as total_reservation_orders
        FROM reservations 
        WHERE YEAR(document_date) = '$current_year'
        GROUP BY MONTH(document_date), <PERSON><PERSON><PERSON><PERSON><PERSON>(document_date)
        ORDER BY MONTH(document_date)
    ";
    
    $result = mysqli_query($conn, $monthly_sales_query);
    $reservations_result = mysqli_query($conn, $monthly_reservations_query);
    
    if (!$result || !$reservations_result) {
        throw new Exception('Database query failed: ' . mysqli_error($conn));
    }
    
    $monthly_data = [];
    $months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    
    // Initialize all months with zero values
    foreach ($months as $index => $month) {
        $monthly_data[] = [
            'month' => $month,
            'month_number' => $index + 1,
            'total_sales' => 0,
            'total_orders' => 0,
            'total_reservations' => 0,
            'total_reservation_orders' => 0
        ];
    }
    
    // Fill in sales data
    while ($row = mysqli_fetch_assoc($result)) {
        $month_index = $row['month'] - 1;
        $monthly_data[$month_index]['total_sales'] = (float)$row['total_sales'];
        $monthly_data[$month_index]['total_orders'] = (int)$row['total_orders'];
    }
    
    // Fill in reservations data
    while ($row = mysqli_fetch_assoc($reservations_result)) {
        $month_index = $row['month'] - 1;
        $monthly_data[$month_index]['total_reservations'] = (float)$row['total_reservations'];
        $monthly_data[$month_index]['total_reservation_orders'] = (int)$row['total_reservation_orders'];
    }
    
    // Get comparison data for previous year
    $previous_year = $current_year - 1;
    $previous_year_query = "
        SELECT 
            MONTH(document_date) as month,
            SUM(grand_total) as total_sales
        FROM invoices 
        WHERE YEAR(document_date) = '$previous_year'
        GROUP BY MONTH(document_date)
        ORDER BY MONTH(document_date)
    ";
    
    $prev_result = mysqli_query($conn, $previous_year_query);
    $previous_year_data = array_fill(0, 12, 0);
    
    if ($prev_result) {
        while ($row = mysqli_fetch_assoc($prev_result)) {
            $previous_year_data[$row['month'] - 1] = (float)$row['total_sales'];
        }
    }
    
    // Prepare response
    $response = [
        'success' => true,
        'current_year' => $current_year,
        'previous_year' => $previous_year,
        'monthly_data' => $monthly_data,
        'previous_year_data' => $previous_year_data
    ];
    
    echo json_encode($response);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}

mysqli_close($conn);
?>



