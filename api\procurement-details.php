<?php
include '../config.php';

// get procurement details data by $_GET['procurement_id']
if (isset($_GET['procurement_id'])) {
    $procurement_id = $_GET['procurement_id'];
    $sql = "SELECT d.*, 
                   p.profile_image,
                   c.customer_code,
                   c.short_name as customer_short_name,
                   c.fullname as customer_name,
                   r.document_number as reservation_document_number
            FROM procurement_details d
            LEFT JOIN products_orders p ON d.product_id=p.id
            LEFT JOIN customers c ON d.customer_id=c.customer_code OR d.customer_id=c.id
            LEFT JOIN reservations r ON d.reservation_id=r.id
            WHERE d.procurement_id = $procurement_id
            ORDER BY d.id";
    $result = mysqli_query($conn, $sql);
    $data = [];

    while ($row = mysqli_fetch_assoc($result)) {
        $data[] = $row;
    }
    echo json_encode($data);
}
