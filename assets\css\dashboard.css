/* Dashboard Custom Styles */
.dashboard-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 30px;
}

.small-box {
    position: relative;
    display: block;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border-radius: 12px;
    overflow: hidden;
    transition: all 0.3s ease;
    background: linear-gradient(135deg, var(--box-color) 0%, var(--box-color-dark) 100%);
}

.small-box:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(0,0,0,0.15);
}

.small-box .inner {
    padding: 20px;
    color: #fff;
    position: relative;
    z-index: 2;
}

.small-box .inner h3 {
    font-size: 2.2rem;
    font-weight: 700;
    margin-bottom: 5px;
    text-shadow: 0 1px 3px rgba(0,0,0,0.3);
}

.small-box .inner h5 {
    font-size: 1rem;
    font-weight: 500;
    opacity: 0.9;
}

.small-box .small-box-icon {
    position: absolute;
    top: 10px;
    right: 15px;
    z-index: 1;
    font-size: 80px;
    color: rgba(255,255,255,0.2);
}

.small-box .small-box-footer {
    position: relative;
    text-align: center;
    padding: 8px 0;
    color: #fff;
    display: block;
    z-index: 10;
    background: rgba(0,0,0,0.1);
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
}

.small-box .small-box-footer:hover {
    color: #fff;
    background: rgba(0,0,0,0.2);
    text-decoration: none;
}

/* Color Variations */
.text-bg-primary {
    --box-color: #4e73df;
    --box-color-dark: #2e59d9;
}

.text-bg-success {
    --box-color: #1cc88a;
    --box-color-dark: #17a673;
}

.text-bg-info {
    --box-color: #36b9cc;
    --box-color-dark: #2c9faf;
}

.text-bg-warning {
    --box-color: #f6c23e;
    --box-color-dark: #dda20a;
}

.text-bg-danger {
    --box-color: #e74a3b;
    --box-color-dark: #c0392b;
}

.text-bg-secondary {
    --box-color: #858796;
    --box-color-dark: #5a5c69;
}

/* Card Styles */
.card {
    border: none;
    border-radius: 12px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0,0,0,0.08);
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.12);
}

.card-header {
    border-radius: 12px 12px 0 0 !important;
    border-bottom: none;
    font-weight: 600;
    background: linear-gradient(135deg, var(--header-color) 0%, var(--header-color-dark) 100%);
}

.card-header.bg-primary {
    --header-color: #4e73df;
    --header-color-dark: #2e59d9;
}

.card-header.bg-success {
    --header-color: #1cc88a;
    --header-color-dark: #17a673;
}

.card-header.bg-info {
    --header-color: #36b9cc;
    --header-color-dark: #2c9faf;
}

.card-header.bg-warning {
    --header-color: #f6c23e;
    --header-color-dark: #dda20a;
}

.card-body {
    padding: 1.5rem;
}

/* Table Styles */
.table {
    margin-bottom: 0;
}

.table th {
    border-top: none;
    font-weight: 600;
    background-color: #f8f9fc;
    color: #5a5c69;
}

.table-hover tbody tr:hover {
    background-color: rgba(0,0,0,0.02);
}

.badge {
    font-size: 0.8em;
    padding: 0.4em 0.8em;
    border-radius: 6px;
}

/* Chart Container */
.chart-container {
    position: relative;
    margin: auto;
    height: 400px;
    width: 100%;
}

.chart-container canvas {
    max-height: 400px;
}

/* Section Headers */
.section-header {
    margin-bottom: 1.5rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #e3e6f0;
}

.section-header h4 {
    margin-bottom: 0;
    font-weight: 600;
}

/* Product List Styles */
.product-item {
    transition: all 0.3s ease;
    border-radius: 8px;
    padding: 0.5rem;
}

.product-item:hover {
    background-color: #f8f9fc;
    transform: translateX(5px);
}

.product-image {
    width: 50px;
    height: 50px;
    object-fit: cover;
    border-radius: 8px;
    border: 2px solid #e3e6f0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .small-box .inner h3 {
        font-size: 1.8rem;
    }
    
    .small-box .inner h5 {
        font-size: 0.9rem;
    }
    
    .small-box .small-box-icon {
        font-size: 60px;
        top: 5px;
        right: 10px;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .chart-container {
        height: 300px;
    }
}

/* Animation for page load */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Utility Classes */
.text-gray-800 {
    color: #5a5c69 !important;
}

.shadow-sm {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important;
}

.rounded-3 {
    border-radius: 12px !important;
}

/* Loading Animation */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255,255,255,.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Material Selection Lock Styles */
.select2-container.select2-container--disabled .select2-selection {
    background-color: #f8f9fa !important;
    border-color: #e9ecef !important;
    cursor: not-allowed !important;
    opacity: 0.7;
}

.select2-container.select2-container--disabled .select2-selection__rendered {
    color: #6c757d !important;
}

.select2-container.select2-container--disabled .select2-selection__arrow {
    display: none !important;
}

/* Material locked indicator */
.material-locked::after {
    content: " 🔒";
    color: #dc3545;
    font-size: 0.8em;
}

/* Tooltip for locked material */
.material-locked {
    position: relative;
}

.material-locked:hover::before {
    content: "วัตถุดิบถูกล็อคแล้ว ไม่สามารถเปลี่ยนแปลงได้";
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background-color: #343a40;
    color: white;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    z-index: 1000;
}

/* Unlock button styles */
.btn-warning.btn-sm {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
}

.btn-warning:hover {
    background-color: #e0a800;
    border-color: #d39e00;
}

/* Material selection visual feedback */
.select2-container--disabled .select2-selection--single {
    background-color: #f8f9fa !important;
    border: 1px solid #dee2e6 !important;
    cursor: not-allowed !important;
}

.select2-container--disabled .select2-selection__rendered {
    color: #6c757d !important;
    text-decoration: none !important;
}

/* Add subtle animation for locked state */
.material-locked {
    transition: all 0.3s ease;
}

.material-locked:hover {
    background-color: rgba(248, 249, 250, 0.5);
}
