<?php
include '../config.php';
include '../auth.php';

// get product data by $_GET['product_id'] and $_GET['reservation_id']
if (isset($_GET['product_id']) && isset($_GET['reservation_id'])) {
    $sql = "SELECT rd.*, p.profile_image, mt.materials_name, h.hardness_name
    FROM reservation_details rd 
    INNER JOIN products p 
    ON rd.product_id = p.id 
    INNER JOIN materials mt 
    ON p.material_id = mt.id 
    INNER JOIN hardness h 
    ON p.hardnes_id = h.id 
    WHERE rd.reservation_id = " . intval($_GET['reservation_id']) . " 
    AND rd.product_id = " . intval($_GET['product_id']);    
    
    $result = mysqli_query($conn, $sql);
    $data = mysqli_fetch_assoc($result);
    echo json_encode($data);
} else {
    echo json_encode(['error' => 'Product ID or Reservation ID not provided']);
}
