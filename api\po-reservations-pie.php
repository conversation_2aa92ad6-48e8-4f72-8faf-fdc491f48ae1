<?php
include '../config.php';

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type');

try {
    // Get current year
    $current_year = date('Y');
    $first_day_of_month = date('Y-m-01');
    $current_date = date('Y-m-d');
    
    // Query to get reservation data by PO for current month
    $po_reservations_query = "
        SELECT 
            r.purchase_order,
            COUNT(r.id) as reservation_count,
            SUM(r.grand_total) as total_amount,
            ROUND((COUNT(r.id) * 100.0 / (
                SELECT COUNT(*) 
                FROM reservations r2 
                WHERE r2.document_date BETWEEN '$first_day_of_month' AND '$current_date'
                AND r2.purchase_order IS NOT NULL 
                AND r2.purchase_order != ''
            )), 2) as percentage
        FROM reservations r
        WHERE r.document_date BETWEEN '$first_day_of_month' AND '$current_date'
        AND r.purchase_order IS NOT NULL 
        AND r.purchase_order != ''
        GROUP BY r.purchase_order
        ORDER BY total_amount DESC
        LIMIT 10
    ";
    
    $result = mysqli_query($conn, $po_reservations_query);
    
    if (!$result) {
        throw new Exception('Database query failed: ' . mysqli_error($conn));
    }
    
    $po_data = [];
    $total_reservations = 0;
    $total_amount = 0;
    
    while ($row = mysqli_fetch_assoc($result)) {
        $po_data[] = [
            'purchase_order' => $row['purchase_order'],
            'reservation_count' => (int)$row['reservation_count'],
            'total_amount' => (float)$row['total_amount'],
            'percentage' => (float)$row['percentage']
        ];
        $total_reservations += (int)$row['reservation_count'];
        $total_amount += (float)$row['total_amount'];
    }
    
    // If we have fewer than 10 POs, add "อื่นๆ" for the rest
    if (count($po_data) > 0) {
        // Get total reservations for the period
        $total_reservations_query = "
            SELECT COUNT(*) as total 
            FROM reservations 
            WHERE document_date BETWEEN '$first_day_of_month' AND '$current_date'
            AND purchase_order IS NOT NULL 
            AND purchase_order != ''
        ";
        $total_result = mysqli_query($conn, $total_reservations_query);
        $total_row = mysqli_fetch_assoc($total_result);
        $all_reservations = (int)$total_row['total'];
        
        // Calculate remaining reservations
        $remaining_reservations = $all_reservations - $total_reservations;
        
        if ($remaining_reservations > 0) {
            // Get remaining amount
            $remaining_amount_query = "
                SELECT COALESCE(SUM(r.grand_total), 0) as remaining_amount
                FROM reservations r
                WHERE r.document_date BETWEEN '$first_day_of_month' AND '$current_date'
                AND r.purchase_order IS NOT NULL 
                AND r.purchase_order != ''
                AND r.purchase_order NOT IN (
                    SELECT DISTINCT r2.purchase_order 
                    FROM reservations r2
                    WHERE r2.document_date BETWEEN '$first_day_of_month' AND '$current_date'
                    AND r2.purchase_order IS NOT NULL 
                    AND r2.purchase_order != ''
                    GROUP BY r2.purchase_order
                    ORDER BY SUM(r2.grand_total) DESC
                    LIMIT 10
                )
            ";
            $remaining_result = mysqli_query($conn, $remaining_amount_query);
            $remaining_row = mysqli_fetch_assoc($remaining_result);
            $remaining_amount = (float)$remaining_row['remaining_amount'];
            
            if ($remaining_amount > 0) {
                $po_data[] = [
                    'purchase_order' => 'PO อื่นๆ',
                    'reservation_count' => $remaining_reservations,
                    'total_amount' => $remaining_amount,
                    'percentage' => round(($remaining_reservations * 100.0 / $all_reservations), 2)
                ];
            }
        }
    }
    
    // Prepare response
    $response = [
        'success' => true,
        'data' => $po_data,
        'summary' => [
            'total_pos' => count($po_data),
            'total_reservations' => $total_reservations,
            'total_amount' => $total_amount,
            'period' => [
                'start' => $first_day_of_month,
                'end' => $current_date
            ]
        ]
    ];
    
    echo json_encode($response);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}

mysqli_close($conn);
?>
