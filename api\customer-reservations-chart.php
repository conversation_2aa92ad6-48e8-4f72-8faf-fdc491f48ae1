<?php
include '../config.php';

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type');

try {
    // Get current year
    $current_year = date('Y');
    $first_day_of_month = date('Y-m-01');
    $current_date = date('Y-m-d');
      // Query to get reservation data by customer for current month
    $customer_reservations_query = "
        SELECT 
            c.id as customer_id,
            c.fullname as customer_name,
            c.short_name,
            COUNT(r.id) as reservation_count,
            SUM(r.grand_total) as total_amount,
            ROUND((SUM(r.grand_total) * 100.0 / (
                SELECT SUM(grand_total) 
                FROM reservations r2 
                WHERE r2.document_date BETWEEN '$first_day_of_month' AND '$current_date'
            )), 2) as percentage
        FROM customers c
        INNER JOIN reservations r ON c.id = r.customer_id
        WHERE r.document_date BETWEEN '$first_day_of_month' AND '$current_date'
        GROUP BY c.id, c.fullname, c.short_name
        ORDER BY total_amount DESC
        LIMIT 10
    ";
    
    $result = mysqli_query($conn, $customer_reservations_query);
    
    if (!$result) {
        throw new Exception('Database query failed: ' . mysqli_error($conn));
    }
    
    $customer_data = [];
    $total_reservations = 0;
    $total_amount = 0;
    
    while ($row = mysqli_fetch_assoc($result)) {
        $customer_data[] = [
            'customer_id' => (int)$row['customer_id'],
            'customer_name' => $row['customer_name'],
            'short_name' => $row['short_name'],
            'reservation_count' => (int)$row['reservation_count'],
            'total_amount' => (float)$row['total_amount'],
            'percentage' => (float)$row['percentage']
        ];
        $total_reservations += (int)$row['reservation_count'];
        $total_amount += (float)$row['total_amount'];
    }
      // If we have fewer than 10 customers, add "อื่นๆ" for the rest
    if (count($customer_data) > 0) {
        // Get total amount for the period
        $total_amount_query = "
            SELECT COALESCE(SUM(grand_total), 0) as total 
            FROM reservations 
            WHERE document_date BETWEEN '$first_day_of_month' AND '$current_date'
        ";
        $total_amount_result = mysqli_query($conn, $total_amount_query);
        $total_amount_row = mysqli_fetch_assoc($total_amount_result);
        $all_amount = (float)$total_amount_row['total'];
        
        // Calculate remaining amount
        $remaining_amount = $all_amount - $total_amount;
        
        if ($remaining_amount > 0) {
            // Get total reservations for the period
            $total_reservations_query = "
                SELECT COUNT(*) as total 
                FROM reservations 
                WHERE document_date BETWEEN '$first_day_of_month' AND '$current_date'
            ";
            $total_result = mysqli_query($conn, $total_reservations_query);
            $total_row = mysqli_fetch_assoc($total_result);
            $all_reservations = (int)$total_row['total'];
            
            // Calculate remaining reservations
            $remaining_reservations = $all_reservations - $total_reservations;
            
            if ($remaining_reservations > 0) {
                $remaining_percentage = round(($remaining_amount * 100.0 / $all_amount), 2);
                
                $customer_data[] = [
                    'customer_id' => 0,
                    'customer_name' => 'ลูกค้าอื่นๆ',
                    'short_name' => 'อื่นๆ',
                    'reservation_count' => $remaining_reservations,
                    'total_amount' => $remaining_amount,
                    'percentage' => $remaining_percentage
                ];
            }
        }
    }
    
    // Prepare response
    $response = [
        'success' => true,
        'data' => $customer_data,
        'summary' => [
            'total_customers' => count($customer_data),
            'total_reservations' => $total_reservations,
            'total_amount' => $total_amount,
            'period' => [
                'start' => $first_day_of_month,
                'end' => $current_date
            ]
        ]
    ];
    
    echo json_encode($response);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}

mysqli_close($conn);
?>