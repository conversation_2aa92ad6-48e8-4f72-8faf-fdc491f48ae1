<?php
include '../config.php';
include '../auth.php';

// Set content type to JSON
header('Content-Type: application/json');

// Function to generate next number
function generateNextNumber($conn, $prefix) {
    $query = "SELECT current_number, format FROM running_numbers WHERE prefix = ?";
    $stmt = $conn->prepare($query);
    $stmt->bind_param("s", $prefix);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($row = $result->fetch_assoc()) {
        $next_number = $row['current_number'] + 1;
        $format = $row['format'];
        
        // Update the running number
        $update_query = "UPDATE running_numbers SET current_number = ? WHERE prefix = ?";
        $update_stmt = $conn->prepare($update_query);
        $update_stmt->bind_param("is", $next_number, $prefix);
        $update_stmt->execute();
        
        // Format the number
        $year = date('Y');
        $month = date('m');
        $day = date('d');
        $number = str_pad($next_number, 4, '0', STR_PAD_LEFT);
        
        $formatted_number = str_replace(
            ['{YYYY}', '{MM}', '{DD}', '{####}'],
            [$year, $month, $day, $number],
            $format
        );
        
        return $formatted_number;
    }
    
    return false;
}

// Get request method
$method = $_SERVER['REQUEST_METHOD'];

switch ($method) {
    case 'GET':
        if (isset($_GET['action'])) {
            switch ($_GET['action']) {
                case 'list':
                    // Get all account payables with pagination and filters
                    $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
                    $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 10;
                    $offset = ($page - 1) * $limit;
                    
                    $where_conditions = [];
                    $params = [];
                    $types = "";
                    
                    // Filter by supplier
                    if (isset($_GET['supplier_id']) && !empty($_GET['supplier_id'])) {
                        $where_conditions[] = "ap.supplier_id = ?";
                        $params[] = $_GET['supplier_id'];
                        $types .= "i";
                    }
                    
                    // Filter by status
                    if (isset($_GET['status']) && !empty($_GET['status'])) {
                        $where_conditions[] = "ap.status = ?";
                        $params[] = $_GET['status'];
                        $types .= "s";
                    }
                    
                    // Filter by due date range
                    if (isset($_GET['due_date_from']) && !empty($_GET['due_date_from'])) {
                        $where_conditions[] = "ap.due_date >= ?";
                        $params[] = $_GET['due_date_from'];
                        $types .= "s";
                    }
                    
                    if (isset($_GET['due_date_to']) && !empty($_GET['due_date_to'])) {
                        $where_conditions[] = "ap.due_date <= ?";
                        $params[] = $_GET['due_date_to'];
                        $types .= "s";
                    }
                    
                    $where_clause = !empty($where_conditions) ? "WHERE " . implode(" AND ", $where_conditions) : "";
                    
                    $query = "
                        SELECT 
                            ap.*,
                            s.fullname as supplier_name,
                            s.supplier_code,
                            u.fullname as created_by_name
                        FROM account_payables ap
                        LEFT JOIN suppliers s ON ap.supplier_id = s.id
                        LEFT JOIN users u ON ap.created_by = u.id
                        {$where_clause}
                        ORDER BY ap.created_at DESC
                        LIMIT ? OFFSET ?
                    ";
                    
                    $stmt = $conn->prepare($query);
                    $params[] = $limit;
                    $params[] = $offset;
                    $types .= "ii";
                    
                    if (!empty($params)) {
                        $stmt->bind_param($types, ...$params);
                    }
                    
                    $stmt->execute();
                    $result = $stmt->get_result();
                    $account_payables = [];
                    
                    while ($row = $result->fetch_assoc()) {
                        $account_payables[] = $row;
                    }
                    
                    // Get total count
                    $count_query = "SELECT COUNT(*) as total FROM account_payables ap {$where_clause}";
                    $count_stmt = $conn->prepare($count_query);
                    
                    if (!empty($where_conditions)) {
                        $count_types = substr($types, 0, -2); // Remove last 'ii' for limit and offset
                        $count_params = array_slice($params, 0, -2); // Remove last 2 params
                        $count_stmt->bind_param($count_types, ...$count_params);
                    }
                    
                    $count_stmt->execute();
                    $count_result = $count_stmt->get_result();
                    $total = $count_result->fetch_assoc()['total'];
                    
                    echo json_encode([
                        'success' => true,
                        'data' => $account_payables,
                        'total' => $total,
                        'page' => $page,
                        'limit' => $limit,
                        'total_pages' => ceil($total / $limit)
                    ]);
                    break;
                    
                case 'get':
                    // Get single account payable
                    if (!isset($_GET['id'])) {
                        echo json_encode(['success' => false, 'message' => 'ID is required']);
                        exit;
                    }
                    
                    $id = $_GET['id'];
                    $query = "
                        SELECT 
                            ap.*,
                            s.fullname as supplier_name,
                            s.supplier_code,
                            s.tel as supplier_tel,
                            s.address as supplier_address,
                            u.fullname as created_by_name
                        FROM account_payables ap
                        LEFT JOIN suppliers s ON ap.supplier_id = s.id
                        LEFT JOIN users u ON ap.created_by = u.id
                        WHERE ap.id = ?
                    ";
                    
                    $stmt = $conn->prepare($query);
                    $stmt->bind_param("i", $id);
                    $stmt->execute();
                    $result = $stmt->get_result();
                    
                    if ($row = $result->fetch_assoc()) {
                        // Get payments
                        $payment_query = "
                            SELECT 
                                app.*,
                                u.fullname as created_by_name
                            FROM account_payable_payments app
                            LEFT JOIN users u ON app.created_by = u.id
                            WHERE app.account_payable_id = ?
                            ORDER BY app.payment_date DESC
                        ";
                        
                        $payment_stmt = $conn->prepare($payment_query);
                        $payment_stmt->bind_param("i", $id);
                        $payment_stmt->execute();
                        $payment_result = $payment_stmt->get_result();
                        $payments = [];
                        
                        while ($payment_row = $payment_result->fetch_assoc()) {
                            $payments[] = $payment_row;
                        }
                        
                        // Get adjustments
                        $adjustment_query = "
                            SELECT 
                                apa.*,
                                u1.fullname as created_by_name,
                                u2.fullname as approved_by_name
                            FROM account_payable_adjustments apa
                            LEFT JOIN users u1 ON apa.created_by = u1.id
                            LEFT JOIN users u2 ON apa.approved_by = u2.id
                            WHERE apa.account_payable_id = ?
                            ORDER BY apa.adjustment_date DESC
                        ";
                        
                        $adjustment_stmt = $conn->prepare($adjustment_query);
                        $adjustment_stmt->bind_param("i", $id);
                        $adjustment_stmt->execute();
                        $adjustment_result = $adjustment_stmt->get_result();
                        $adjustments = [];
                        
                        while ($adjustment_row = $adjustment_result->fetch_assoc()) {
                            $adjustments[] = $adjustment_row;
                        }
                        
                        $row['payments'] = $payments;
                        $row['adjustments'] = $adjustments;
                        
                        echo json_encode(['success' => true, 'data' => $row]);
                    } else {
                        echo json_encode(['success' => false, 'message' => 'Account payable not found']);
                    }
                    break;
                    
                case 'dashboard':
                    // Get dashboard summary
                    $summary_query = "
                        SELECT 
                            COUNT(*) as total_count,
                            SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending_count,
                            SUM(CASE WHEN status = 'overdue' THEN 1 ELSE 0 END) as overdue_count,
                            SUM(CASE WHEN status = 'paid' THEN 1 ELSE 0 END) as paid_count,
                            SUM(remaining_amount) as total_remaining,
                            SUM(CASE WHEN due_date < CURDATE() AND status != 'paid' THEN remaining_amount ELSE 0 END) as overdue_amount,
                            SUM(CASE WHEN due_date BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 7 DAY) AND status != 'paid' THEN remaining_amount ELSE 0 END) as due_within_week
                        FROM account_payables
                    ";
                    
                    $result = mysqli_query($conn, $summary_query);
                    $summary = mysqli_fetch_assoc($result);
                    
                    echo json_encode(['success' => true, 'data' => $summary]);
                    break;
                    
                default:
                    echo json_encode(['success' => false, 'message' => 'Invalid action']);
            }
        } else {
            echo json_encode(['success' => false, 'message' => 'Action is required']);
        }
        break;
        
    case 'POST':
        // Create new account payable
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (!$input) {
            echo json_encode(['success' => false, 'message' => 'Invalid JSON input']);
            exit;
        }
        
        // Validate required fields
        $required_fields = ['supplier_id', 'bill_date', 'due_date', 'amount'];
        foreach ($required_fields as $field) {
            if (!isset($input[$field]) || empty($input[$field])) {
                echo json_encode(['success' => false, 'message' => "Field {$field} is required"]);
                exit;
            }
        }
        
        // Generate AP number
        $ap_number = generateNextNumber($conn, 'AP');
        if (!$ap_number) {
            echo json_encode(['success' => false, 'message' => 'Failed to generate AP number']);
            exit;
        }
        
        // Calculate amounts
        $amount = (float)$input['amount'];
        $vat_amount = isset($input['vat_amount']) ? (float)$input['vat_amount'] : 0;
        $withholding_tax = isset($input['withholding_tax']) ? (float)$input['withholding_tax'] : 0;
        $net_amount = $amount + $vat_amount - $withholding_tax;
        
        $query = "
            INSERT INTO account_payables (
                ap_number, supplier_id, bill_id, invoice_number, bill_date, due_date,
                amount, vat_amount, withholding_tax, net_amount, remaining_amount,
                status, description, created_by, created_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
        ";
        
        $stmt = $conn->prepare($query);
        $stmt->bind_param(
            "siisssddddssi",
            $ap_number,
            $input['supplier_id'],
            $input['bill_id'] ?? null,
            $input['invoice_number'] ?? null,
            $input['bill_date'],
            $input['due_date'],
            $amount,
            $vat_amount,
            $withholding_tax,
            $net_amount,
            $net_amount, // remaining_amount = net_amount initially
            $input['status'] ?? 'pending',
            $input['description'] ?? null,
            $_SESSION['user_id']
        );
        
        if ($stmt->execute()) {
            $new_id = $conn->insert_id;
            echo json_encode([
                'success' => true, 
                'message' => 'Account payable created successfully',
                'id' => $new_id,
                'ap_number' => $ap_number
            ]);
        } else {
            echo json_encode(['success' => false, 'message' => 'Failed to create account payable: ' . $conn->error]);
        }
        break;
        
    case 'PUT':
        // Update account payable
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (!$input || !isset($input['id'])) {
            echo json_encode(['success' => false, 'message' => 'ID is required']);
            exit;
        }
        
        $id = $input['id'];
        $set_clauses = [];
        $params = [];
        $types = "";
        
        $updatable_fields = [
            'supplier_id' => 'i',
            'bill_id' => 'i',
            'invoice_number' => 's',
            'bill_date' => 's',
            'due_date' => 's',
            'amount' => 'd',
            'vat_amount' => 'd',
            'withholding_tax' => 'd',
            'status' => 's',
            'description' => 's'
        ];
        
        foreach ($updatable_fields as $field => $type) {
            if (isset($input[$field])) {
                $set_clauses[] = "{$field} = ?";
                $params[] = $input[$field];
                $types .= $type;
            }
        }
        
        if (empty($set_clauses)) {
            echo json_encode(['success' => false, 'message' => 'No fields to update']);
            exit;
        }
        
        // Recalculate net amount if amounts changed
        if (isset($input['amount']) || isset($input['vat_amount']) || isset($input['withholding_tax'])) {
            // Get current values if not provided
            $current_query = "SELECT amount, vat_amount, withholding_tax, paid_amount FROM account_payables WHERE id = ?";
            $current_stmt = $conn->prepare($current_query);
            $current_stmt->bind_param("i", $id);
            $current_stmt->execute();
            $current_result = $current_stmt->get_result();
            $current = $current_result->fetch_assoc();
            
            $amount = isset($input['amount']) ? (float)$input['amount'] : (float)$current['amount'];
            $vat_amount = isset($input['vat_amount']) ? (float)$input['vat_amount'] : (float)$current['vat_amount'];
            $withholding_tax = isset($input['withholding_tax']) ? (float)$input['withholding_tax'] : (float)$current['withholding_tax'];
            $paid_amount = (float)$current['paid_amount'];
            
            $net_amount = $amount + $vat_amount - $withholding_tax;
            $remaining_amount = $net_amount - $paid_amount;
            
            $set_clauses[] = "net_amount = ?";
            $set_clauses[] = "remaining_amount = ?";
            $params[] = $net_amount;
            $params[] = $remaining_amount;
            $types .= "dd";
        }
        
        $set_clauses[] = "updated_by = ?";
        $set_clauses[] = "updated_at = NOW()";
        $params[] = $_SESSION['user_id'];
        $types .= "i";
        
        $params[] = $id;
        $types .= "i";
        
        $query = "UPDATE account_payables SET " . implode(", ", $set_clauses) . " WHERE id = ?";
        $stmt = $conn->prepare($query);
        $stmt->bind_param($types, ...$params);
        
        if ($stmt->execute()) {
            echo json_encode(['success' => true, 'message' => 'Account payable updated successfully']);
        } else {
            echo json_encode(['success' => false, 'message' => 'Failed to update account payable: ' . $conn->error]);
        }
        break;
        
    case 'DELETE':
        // Delete account payable
        if (!isset($_GET['id'])) {
            echo json_encode(['success' => false, 'message' => 'ID is required']);
            exit;
        }
        
        $id = $_GET['id'];
        
        // Check if there are any payments
        $payment_check = "SELECT COUNT(*) as count FROM account_payable_payments WHERE account_payable_id = ?";
        $stmt = $conn->prepare($payment_check);
        $stmt->bind_param("i", $id);
        $stmt->execute();
        $result = $stmt->get_result();
        $count = $result->fetch_assoc()['count'];
        
        if ($count > 0) {
            echo json_encode(['success' => false, 'message' => 'Cannot delete account payable with existing payments']);
            exit;
        }
        
        // Delete adjustments first
        $delete_adjustments = "DELETE FROM account_payable_adjustments WHERE account_payable_id = ?";
        $stmt = $conn->prepare($delete_adjustments);
        $stmt->bind_param("i", $id);
        $stmt->execute();
        
        // Delete account payable
        $delete_query = "DELETE FROM account_payables WHERE id = ?";
        $stmt = $conn->prepare($delete_query);
        $stmt->bind_param("i", $id);
        
        if ($stmt->execute()) {
            echo json_encode(['success' => true, 'message' => 'Account payable deleted successfully']);
        } else {
            echo json_encode(['success' => false, 'message' => 'Failed to delete account payable: ' . $conn->error]);
        }
        break;
        
    default:
        echo json_encode(['success' => false, 'message' => 'Method not allowed']);
}

$conn->close();
?>
