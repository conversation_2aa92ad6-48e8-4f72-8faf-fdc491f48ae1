<?php
include '../config.php';

header('Content-Type: application/json');

try {
    if (isset($_GET['id']) && !empty($_GET['id'])) {
        // Get single material by ID
        $id = mysqli_real_escape_string($conn, $_GET['id']);
        $query = "SELECT * FROM materials WHERE id = '$id'";
        $result = mysqli_query($conn, $query);
        
        if ($row = mysqli_fetch_assoc($result)) {
            echo json_encode($row);
        } else {
            echo json_encode(['error' => 'Material not found']);
        }
    } else {
        // Get all materials
        $search = isset($_GET['search']) ? mysqli_real_escape_string($conn, $_GET['search']) : '';
        $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 100;
        $offset = isset($_GET['offset']) ? (int)$_GET['offset'] : 0;
        
        $query = "SELECT id, materials_name FROM materials WHERE 1=1";
        
        if (!empty($search)) {
            $query .= " AND materials_name LIKE '%$search%'";
        }
        
        $query .= " ORDER BY materials_name ASC";
        
        if ($limit > 0) {
            $query .= " LIMIT $limit OFFSET $offset";
        }
        
        $result = mysqli_query($conn, $query);
        $materials = [];
        
        while ($row = mysqli_fetch_assoc($result)) {
            $materials[] = $row;
        }
        
        echo json_encode($materials);
    }
} catch (Exception $e) {
    echo json_encode(['error' => $e->getMessage()]);
}

mysqli_close($conn);
?>
