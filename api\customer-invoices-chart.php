<?php
include '../config.php';

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type');

try {
    // Get current year
    $current_year = date('Y');
    $first_day_of_month = date('Y-m-01');
    $current_date = date('Y-m-d');
    
    // Query to get invoice data by customer for current month
    $customer_invoices_query = "
        SELECT 
            c.id as customer_id,
            c.fullname as customer_name,
            c.short_name,
            COUNT(i.id) as invoice_count,
            SUM(i.grand_total) as total_amount,
            ROUND((COUNT(i.id) * 100.0 / (
                SELECT COUNT(*) 
                FROM invoices i2 
                WHERE i2.document_date BETWEEN '$first_day_of_month' AND '$current_date'
            )), 2) as percentage
        FROM customers c
        INNER JOIN invoices i ON c.id = i.customer_id
        WHERE i.document_date BETWEEN '$first_day_of_month' AND '$current_date'
        GROUP BY c.id, c.fullname, c.short_name
        ORDER BY total_amount DESC
        LIMIT 10
    ";
    
    $result = mysqli_query($conn, $customer_invoices_query);
    
    if (!$result) {
        throw new Exception('Database query failed: ' . mysqli_error($conn));
    }
    
    $customer_data = [];
    $total_invoices = 0;
    $total_amount = 0;
    
    while ($row = mysqli_fetch_assoc($result)) {
        $customer_data[] = [
            'customer_id' => (int)$row['customer_id'],
            'customer_name' => $row['customer_name'],
            'short_name' => $row['short_name'],
            'invoice_count' => (int)$row['invoice_count'],
            'total_amount' => (float)$row['total_amount'],
            'percentage' => (float)$row['percentage']
        ];
        $total_invoices += (int)$row['invoice_count'];
        $total_amount += (float)$row['total_amount'];
    }
    
    // If we have fewer than 10 customers, add "อื่นๆ" for the rest
    if (count($customer_data) > 0) {
        // Get total invoices for the period
        $total_invoices_query = "
            SELECT COUNT(*) as total 
            FROM invoices 
            WHERE document_date BETWEEN '$first_day_of_month' AND '$current_date'
        ";
        $total_result = mysqli_query($conn, $total_invoices_query);
        $total_row = mysqli_fetch_assoc($total_result);
        $all_invoices = (int)$total_row['total'];
        
        // Calculate remaining invoices
        $remaining_invoices = $all_invoices - $total_invoices;
          if ($remaining_invoices > 0) {
            // First, get the top 10 customer IDs
            $top_customers_query = "
                SELECT DISTINCT c2.id 
                FROM customers c2
                INNER JOIN invoices i2 ON c2.id = i2.customer_id
                WHERE i2.document_date BETWEEN '$first_day_of_month' AND '$current_date'
                GROUP BY c2.id
                ORDER BY SUM(i2.grand_total) DESC
                LIMIT 10
            ";
            $top_customers_result = mysqli_query($conn, $top_customers_query);
            
            $top_customer_ids = [];
            while ($top_row = mysqli_fetch_assoc($top_customers_result)) {
                $top_customer_ids[] = (int)$top_row['id'];
            }
            
            // Now get remaining amount excluding these top customers
            if (!empty($top_customer_ids)) {
                $customer_ids_string = implode(',', $top_customer_ids);
                $remaining_amount_query = "
                    SELECT COALESCE(SUM(i.grand_total), 0) as remaining_amount
                    FROM invoices i
                    LEFT JOIN customers c ON i.customer_id = c.id
                    WHERE i.document_date BETWEEN '$first_day_of_month' AND '$current_date'
                    AND c.id NOT IN ($customer_ids_string)
                ";
            } else {
                $remaining_amount_query = "
                    SELECT COALESCE(SUM(i.grand_total), 0) as remaining_amount
                    FROM invoices i
                    LEFT JOIN customers c ON i.customer_id = c.id
                    WHERE i.document_date BETWEEN '$first_day_of_month' AND '$current_date'
                ";
            }
            $remaining_result = mysqli_query($conn, $remaining_amount_query);
            $remaining_row = mysqli_fetch_assoc($remaining_result);
            $remaining_amount = (float)$remaining_row['remaining_amount'];
            
            if ($remaining_amount > 0) {
                $customer_data[] = [
                    'customer_id' => 0,
                    'customer_name' => 'ลูกค้าอื่นๆ',
                    'short_name' => 'อื่นๆ',
                    'invoice_count' => $remaining_invoices,
                    'total_amount' => $remaining_amount,
                    'percentage' => round(($remaining_invoices * 100.0 / $all_invoices), 2)
                ];
            }
        }
    }
    
    // Prepare response
    $response = [
        'success' => true,
        'data' => $customer_data,
        'summary' => [
            'total_customers' => count($customer_data),
            'total_invoices' => $total_invoices,
            'total_amount' => $total_amount,
            'period' => [
                'start' => $first_day_of_month,
                'end' => $current_date
            ]
        ]
    ];
    
    echo json_encode($response);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}

mysqli_close($conn);
?>
