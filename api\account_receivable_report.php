<?php
include '../config.php';
include '../auth.php';

header('Content-Type: application/json');

// Handle export requests
if (isset($_GET['export']) && $_GET['export'] === 'excel') {
    header('Content-Type: application/vnd.ms-excel');
    header('Content-Disposition: attachment; filename="account_receivable_report_' . date('Y-m-d') . '.xls"');
    
    // Get report type and generate Excel content
    $type = $_GET['type'] ?? 'outstanding';
    echo generateExcelReport($conn, $type);
    exit;
}

function generateExcelReport($conn, $type) {
    // Excel export logic would be implemented here
    return '<table><tr><td>Account Receivable Report</td></tr></table>';
}

$type = $_GET['type'] ?? 'outstanding';

switch ($type) {
    case 'outstanding':
        // รายงานหนี้คงค้าง
        $where_conditions = ["c.outstanding_amount > 0"];
        $params = [];
        $types = "";

        // Filter by customer
        if (isset($_GET['customer_id']) && !empty($_GET['customer_id'])) {
            $where_conditions[] = "c.id = ?";
            $params[] = $_GET['customer_id'];
            $types .= "i";
        }

        // Filter by status
        if (isset($_GET['status']) && !empty($_GET['status'])) {
            $where_conditions[] = "c.payment_status = ?";
            $params[] = $_GET['status'];
            $types .= "s";
        } else {
            // Default: only show unpaid/partial paid
            $where_conditions[] = "c.payment_status IN ('unpaid', 'partial')";
        }

        // Filter by due date range
        if (isset($_GET['due_date_from']) && !empty($_GET['due_date_from'])) {
            $where_conditions[] = "c.due_date >= ?";
            $params[] = $_GET['due_date_from'];
            $types .= "s";
        }

        if (isset($_GET['due_date_to']) && !empty($_GET['due_date_to'])) {
            $where_conditions[] = "c.due_date <= ?";
            $params[] = $_GET['due_date_to'];
            $types .= "s";
        }

        $where_clause = !empty($where_conditions) ? "WHERE " . implode(" AND ", $where_conditions) : "";

        $query = "
            SELECT 
                c.id,
                c.customer_code,
                c.fullname as customer_name,
                c.tel as customer_tel,
                c.contact_name,
                c.outstanding_amount,
                c.due_date,
                c.payment_status,
                DATEDIFF(CURDATE(), c.due_date) as overdue_days,
                DATE(c.created_at) as created_date
            FROM customers c
            {$where_clause}
            ORDER BY c.due_date ASC, c.fullname ASC
        ";

        $stmt = $conn->prepare($query);
        if (!empty($params)) {
            $stmt->bind_param($types, ...$params);
        }

        $stmt->execute();
        $result = $stmt->get_result();
        $data = [];
        $total_amount = 0;
        $total_overdue = 0;

        while ($row = $result->fetch_assoc()) {
            // Add status calculation
            $row['is_overdue'] = $row['overdue_days'] > 0;
            $row['aging_group'] = getAgingGroup($row['overdue_days']);
            
            $data[] = $row;
            $total_amount += $row['outstanding_amount'];
            
            if ($row['is_overdue']) {
                $total_overdue += $row['outstanding_amount'];
            }
        }

        echo json_encode([
            'success' => true,
            'data' => $data,
            'summary' => [
                'total_outstanding' => $total_amount,
                'total_overdue' => $total_overdue,
                'total_count' => count($data)
            ]
        ]);
        break;

    case 'aging':
        // รายงานอายุหนี้
        $where_conditions = ["c.outstanding_amount > 0", "c.payment_status != 'paid'"];
        $params = [];
        $types = "";

        // Filter by customer
        if (isset($_GET['customer_id']) && !empty($_GET['customer_id'])) {
            $where_conditions[] = "c.id = ?";
            $params[] = $_GET['customer_id'];
            $types .= "i";
        }

        $where_clause = !empty($where_conditions) ? "WHERE " . implode(" AND ", $where_conditions) : "";

        $query = "
            SELECT 
                c.id,
                c.customer_code,
                c.fullname as customer_name,
                c.outstanding_amount,
                c.due_date,
                c.payment_status,
                DATEDIFF(CURDATE(), c.due_date) as aging_days,
                CASE 
                    WHEN DATEDIFF(CURDATE(), c.due_date) <= 0 THEN 'current'
                    WHEN DATEDIFF(CURDATE(), c.due_date) BETWEEN 1 AND 30 THEN '1-30'
                    WHEN DATEDIFF(CURDATE(), c.due_date) BETWEEN 31 AND 60 THEN '31-60'
                    WHEN DATEDIFF(CURDATE(), c.due_date) BETWEEN 61 AND 90 THEN '61-90'
                    ELSE 'over-90'
                END as aging_group
            FROM customers c
            {$where_clause}
            ORDER BY c.due_date ASC
        ";

        $stmt = $conn->prepare($query);
        if (!empty($params)) {
            $stmt->bind_param($types, ...$params);
        }

        $stmt->execute();
        $result = $stmt->get_result();
        $data = [];
        $aging_summary = [
            'current' => 0,
            '1-30' => 0,
            '31-60' => 0,
            '61-90' => 0,
            'over-90' => 0
        ];

        while ($row = $result->fetch_assoc()) {
            $data[] = $row;
            $aging_summary[$row['aging_group']] += $row['outstanding_amount'];
        }

        echo json_encode([
            'success' => true,
            'data' => $data,
            'summary' => $aging_summary
        ]);
        break;

    case 'payment_history':
        // รายงานประวัติการรับชำระเงิน
        $where_conditions = [];
        $params = [];
        $types = "";

        // Filter by customer
        if (isset($_GET['customer_id']) && !empty($_GET['customer_id'])) {
            $where_conditions[] = "r.customer_id = ?";
            $params[] = $_GET['customer_id'];
            $types .= "i";
        }

        // Filter by payment date range
        if (isset($_GET['payment_date_from']) && !empty($_GET['payment_date_from'])) {
            $where_conditions[] = "r.receipt_date >= ?";
            $params[] = $_GET['payment_date_from'];
            $types .= "s";
        }

        if (isset($_GET['payment_date_to']) && !empty($_GET['payment_date_to'])) {
            $where_conditions[] = "r.receipt_date <= ?";
            $params[] = $_GET['payment_date_to'];
            $types .= "s";
        }

        $where_clause = !empty($where_conditions) ? "WHERE " . implode(" AND ", $where_conditions) : "";

        $query = "
            SELECT 
                r.id,
                r.receipt_no,
                r.receipt_date,
                r.grand_total as payment_amount,
                c.customer_code,
                c.fullname as customer_name,
                c.tel as customer_tel,
                u.fullname as created_by_name
            FROM receipts r
            LEFT JOIN customers c ON r.customer_id = c.id
            LEFT JOIN users u ON r.user_id = u.id
            {$where_clause}
            ORDER BY r.receipt_date DESC, r.created_at DESC
        ";

        $stmt = $conn->prepare($query);
        if (!empty($params)) {
            $stmt->bind_param($types, ...$params);
        }

        $stmt->execute();
        $result = $stmt->get_result();
        $data = [];
        $total_payments = 0;

        while ($row = $result->fetch_assoc()) {
            $data[] = $row;
            $total_payments += $row['payment_amount'];
        }

        echo json_encode([
            'success' => true,
            'data' => $data,
            'summary' => [
                'total_payments' => $total_payments,
                'payment_count' => count($data)
            ]
        ]);
        break;

    case 'customer_summary':
        // รายงานสรุปตามลูกค้า
        $where_conditions = [];
        $params = [];
        $types = "";

        // Filter by date range
        if (!isset($_GET['date_from']) || !isset($_GET['date_to'])) {
            echo json_encode(['success' => false, 'message' => 'Date range is required']);
            exit;
        }

        $where_conditions[] = "i.invoice_date BETWEEN ? AND ?";
        $params[] = $_GET['date_from'];
        $params[] = $_GET['date_to'];
        $types .= "ss";

        $where_clause = "WHERE " . implode(" AND ", $where_conditions);

        $query = "
            SELECT 
                c.id as customer_id,
                c.customer_code,
                c.fullname as customer_name,
                c.tel as customer_tel,
                c.contact_name,
                COUNT(i.id) as total_invoices,
                SUM(i.grand_total) as total_amount,
                SUM(CASE WHEN c.payment_status = 'paid' THEN i.grand_total ELSE 0 END) as total_paid,
                c.outstanding_amount as total_outstanding,
                c.payment_status,
                c.due_date,
                DATEDIFF(CURDATE(), c.due_date) as overdue_days
            FROM customers c
            LEFT JOIN invoices i ON c.id = i.customer_id
            {$where_clause}
            GROUP BY c.id, c.customer_code, c.fullname, c.tel, c.contact_name, c.outstanding_amount, c.payment_status, c.due_date
            HAVING total_invoices > 0
            ORDER BY total_amount DESC
        ";

        $stmt = $conn->prepare($query);
        if (!empty($params)) {
            $stmt->bind_param($types, ...$params);
        }

        $stmt->execute();
        $result = $stmt->get_result();
        $data = [];
        $summary = [
            'total_customers' => 0,
            'total_invoices' => 0,
            'total_amount' => 0,
            'total_paid' => 0,
            'total_outstanding' => 0
        ];

        while ($row = $result->fetch_assoc()) {
            // Calculate payment percentage
            $row['payment_percentage'] = $row['total_amount'] > 0 ? 
                round(($row['total_paid'] / $row['total_amount']) * 100, 2) : 0;

            $data[] = $row;
            $summary['total_customers']++;
            $summary['total_invoices'] += $row['total_invoices'];
            $summary['total_amount'] += $row['total_amount'];
            $summary['total_paid'] += $row['total_paid'];
            $summary['total_outstanding'] += $row['total_outstanding'];
        }

        echo json_encode([
            'success' => true,
            'data' => $data,
            'summary' => $summary
        ]);
        break;

    case 'monthly_summary':
        // รายงานสรุปรายเดือน
        $year = isset($_GET['year']) ? $_GET['year'] : date('Y');

        $query = "
            SELECT 
                MONTH(i.invoice_date) as month,
                YEAR(i.invoice_date) as year,
                COUNT(i.id) as total_invoices,
                SUM(i.grand_total) as total_amount,
                SUM(CASE WHEN c.payment_status = 'paid' THEN i.grand_total ELSE 0 END) as total_paid,
                SUM(CASE WHEN c.payment_status != 'paid' THEN c.outstanding_amount ELSE 0 END) as total_outstanding,
                COUNT(DISTINCT c.id) as total_customers
            FROM invoices i
            LEFT JOIN customers c ON i.customer_id = c.id
            WHERE YEAR(i.invoice_date) = ? AND i.status_void != 'YES'
            GROUP BY YEAR(i.invoice_date), MONTH(i.invoice_date)
            ORDER BY YEAR(i.invoice_date), MONTH(i.invoice_date)
        ";

        $stmt = $conn->prepare($query);
        $stmt->bind_param("i", $year);
        $stmt->execute();
        $result = $stmt->get_result();

        $data = [];
        $monthly_data = [];

        // Initialize all 12 months with zero values
        for ($i = 1; $i <= 12; $i++) {
            $monthly_data[$i] = [
                'month' => $i,
                'month_name' => date('F', mktime(0, 0, 0, $i, 1)),
                'year' => $year,
                'total_invoices' => 0,
                'total_amount' => 0,
                'total_paid' => 0,
                'total_outstanding' => 0,
                'total_customers' => 0,
                'payment_percentage' => 0
            ];
        }

        // Fill with actual data
        while ($row = $result->fetch_assoc()) {
            $month = $row['month'];
            $monthly_data[$month] = array_merge($monthly_data[$month], $row);
            $monthly_data[$month]['month_name'] = date('F', mktime(0, 0, 0, $month, 1));
            $monthly_data[$month]['payment_percentage'] = $row['total_amount'] > 0 ? 
                round(($row['total_paid'] / $row['total_amount']) * 100, 2) : 0;
        }

        // Convert to array
        $data = array_values($monthly_data);

        // Calculate yearly summary
        $yearly_summary = [
            'total_invoices' => array_sum(array_column($data, 'total_invoices')),
            'total_amount' => array_sum(array_column($data, 'total_amount')),
            'total_paid' => array_sum(array_column($data, 'total_paid')),
            'total_outstanding' => array_sum(array_column($data, 'total_outstanding')),
            'avg_payment_percentage' => 0
        ];

        if ($yearly_summary['total_amount'] > 0) {
            $yearly_summary['avg_payment_percentage'] = round(
                ($yearly_summary['total_paid'] / $yearly_summary['total_amount']) * 100, 2
            );
        }

        echo json_encode([
            'success' => true,
            'data' => $data,
            'summary' => $yearly_summary
        ]);
        break;

    case 'dashboard_summary':
        // รายงานสรุปสำหรับหน้า Dashboard
        $summary_query = "
            SELECT 
                COUNT(*) as total_receivables,
                SUM(outstanding_amount) as total_outstanding,
                SUM(CASE WHEN payment_status = 'unpaid' THEN 1 ELSE 0 END) as unpaid_count,
                SUM(CASE WHEN payment_status = 'partial' THEN 1 ELSE 0 END) as partial_count,
                SUM(CASE WHEN payment_status = 'paid' THEN 1 ELSE 0 END) as paid_count,
                SUM(CASE WHEN due_date < CURDATE() AND payment_status != 'paid' THEN 1 ELSE 0 END) as overdue_count,
                SUM(CASE WHEN due_date < CURDATE() AND payment_status != 'paid' THEN outstanding_amount ELSE 0 END) as overdue_amount,
                SUM(CASE WHEN due_date BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 7 DAY) AND payment_status != 'paid' THEN outstanding_amount ELSE 0 END) as due_within_week,
                SUM(CASE WHEN due_date BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 30 DAY) AND payment_status != 'paid' THEN outstanding_amount ELSE 0 END) as due_within_month
            FROM customers
            WHERE outstanding_amount > 0
        ";

        $result = mysqli_query($conn, $summary_query);
        $summary = mysqli_fetch_assoc($result);

        // Get recent payments
        $recent_payments_query = "
            SELECT 
                r.receipt_no,
                r.receipt_date,
                r.grand_total as payment_amount,
                c.fullname as customer_name
            FROM receipts r
            LEFT JOIN customers c ON r.customer_id = c.id
            ORDER BY r.created_at DESC
            LIMIT 5
        ";

        $recent_payments_result = mysqli_query($conn, $recent_payments_query);
        $recent_payments = [];
        while ($row = mysqli_fetch_assoc($recent_payments_result)) {
            $recent_payments[] = $row;
        }

        // Get upcoming due dates
        $upcoming_due_query = "
            SELECT 
                c.customer_code,
                c.fullname as customer_name,
                c.due_date,
                c.outstanding_amount,
                DATEDIFF(c.due_date, CURDATE()) as days_until_due
            FROM customers c
            WHERE c.payment_status IN ('unpaid', 'partial') 
                AND c.due_date BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 30 DAY)
                AND c.outstanding_amount > 0
            ORDER BY c.due_date ASC
            LIMIT 10
        ";

        $upcoming_due_result = mysqli_query($conn, $upcoming_due_query);
        $upcoming_due = [];
        while ($row = mysqli_fetch_assoc($upcoming_due_result)) {
            $upcoming_due[] = $row;
        }

        echo json_encode([
            'success' => true,
            'summary' => $summary,
            'recent_payments' => $recent_payments,
            'upcoming_due' => $upcoming_due
        ]);
        break;

    default:
        echo json_encode(['success' => false, 'message' => 'Invalid report type']);
}

function getAgingGroup($days) {
    if ($days <= 0) return 'current';
    if ($days <= 30) return '1-30';
    if ($days <= 60) return '31-60';
    if ($days <= 90) return '61-90';
    return 'over-90';
}

$conn->close();
?>
