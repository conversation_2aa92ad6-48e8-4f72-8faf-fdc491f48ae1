<?php
header('Content-Type: application/json');
include '../config.php';
include '../auth.php';

// // get data by $_GET['id']
// if (isset($_GET['id'])) {
//     $id = $_GET['id'];
//     $sql = "SELECT r.*, q.document_no as quotation_no, r.purchase_order as purchase_order_no 
//     FROM reservations r
//     LEFT JOIN quotations q ON r.quotation_id = q.id
//     WHERE r.id = $id";
//     $result = mysqli_query($conn, $sql);
//     $data = array();
//     while ($row = mysqli_fetch_assoc($result)) {
//         $data[] = $row;
//     }
//     echo json_encode($data);
// }
// get reservations by customer_id
if (isset($_GET['customer_id'])) {
    $customer_id = intval($_GET['customer_id']); // ใช้ intval เพื่อความปลอดภัย
    $sql = "SELECT r.*, q.document_no as quotation_no, r.purchase_order as purchase_order_no 
    FROM reservations r
    LEFT JOIN quotations q ON r.quotation_id = q.id    
    WHERE r.customer_id = $customer_id 
    ORDER BY r.id DESC";
    $result = mysqli_query($conn, $sql);
    
    if (!$result) {
        // ถ้า query ผิดพลาด
        echo json_encode(['error' => 'Database query failed: ' . mysqli_error($conn)]);
        exit;
    }
    
    $data = array();
    while ($row = mysqli_fetch_assoc($result)) {
        $data[] = $row;
    }
    
    // เพิ่ม debug information
    error_log("Customer ID: $customer_id, Found reservations: " . count($data));
    
    echo json_encode($data);
}
// get all reservations if no parameters
else {
    $sql = "SELECT * FROM reservations ORDER BY id DESC";
    $result = mysqli_query($conn, $sql);
    $data = array();
    while ($row = mysqli_fetch_assoc($result)) {
        $data[] = $row;
    }
    echo json_encode($data);
}
