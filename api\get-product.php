
<?php
header('Content-Type: application/json');
include '../config.php';
include '../auth.php';

// Check database connection
if (!$conn) {
    echo json_encode(['error' => 'Database connection failed']);
    exit;
}

// get products from reservation details
if (isset($_GET['reservation_details'])) {
    $reservation_id = intval($_GET['reservation_details']);
    
    // ตรวจสอบว่าตาราง reservation_details มีอยู่หรือไม่
    $check_table = "SHOW TABLES LIKE 'reservation_details'";
    $table_result = mysqli_query($conn, $check_table);
    
    if (mysqli_num_rows($table_result) > 0) {
        $sql = "SELECT rd.*, p.product_code, p.product_name, u.unit_name, m.materials_name, h.hardness_name
                FROM reservation_details rd
                LEFT JOIN products p ON rd.product_code = p.product_code
                LEFT JOIN units u ON p.unit_id = u.id
                LEFT JOIN materials m ON p.material_id = m.id
                LEFT JOIN hardness h ON p.hardnes_id = h.id
                WHERE rd.reservation_id = $reservation_id
                ORDER BY rd.id";
        
        $result = mysqli_query($conn, $sql);
        
        if ($result) {
            $data = array();
            while ($row = mysqli_fetch_assoc($result)) {
                $data[] = $row;
            }
            echo json_encode($data);
        } else {
            echo json_encode(['error' => 'Database query failed: ' . mysqli_error($conn)]);
        }
    } else {
        // หากไม่มีตาราง reservation_details ให้ส่งกลับ array ว่าง
        echo json_encode(array());
    }
    exit;
}

// get all products data by $_GET['reservation_id']
if (isset($_GET['all_products'])) {
    $sql = "SELECT p.*, u.unit_name, m.materials_name, h.hardness_name, p.quantity 
    FROM products p, units u, materials m, hardness h 
    WHERE p.unit_id = u.id 
    AND p.material_id = m.id 
    AND p.hardnes_id = h.id 
    ORDER BY p.product_code";
    $result = mysqli_query($conn, $sql);
    
    if ($result) {
        $data = array();
        while ($row = mysqli_fetch_assoc($result)) {
            $data[] = $row;
        }
        echo json_encode($data);
    } else {
        echo json_encode(['error' => 'Database query failed: ' . mysqli_error($conn)]);
    }
    exit;
}


// return empty array if no valid parameters
echo json_encode(array());

